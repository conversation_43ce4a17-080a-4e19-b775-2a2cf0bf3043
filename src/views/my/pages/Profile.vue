<template>
  <div class="info">
    <van-sticky>
      <van-nav-bar title="我的资料" left-arrow @click-left="$router.push({ name: 'My' })" />
    </van-sticky>
    <van-field is-link v-model="pageInfo.username" label="用户名" placeholder="请输入用户名" disabled>
      <template #left-icon> <img src="@/assets/imgs/my/name.png" alt="" width="16" height="16" /> </template
    ></van-field>
    <van-field is-link v-model="pageInfo.name" disabled label="姓名" placeholder="请输入姓名">
      <template #left-icon>
        <img src="@/assets/imgs/my/name.png" alt="" width="16" height="16" />
      </template>
    </van-field>
    <van-field is-link v-model="pageInfo.idcard_no" label="身份证号" placeholder="请输入身份证号" @blur="changeFrom">
      <template #left-icon>
        <img src="@/assets/imgs/my/idcard.png" alt="" width="16" height="16" />
      </template>
    </van-field>
    <van-field is-link v-model="pageInfo.phone_number" label="手机" placeholder="请输入手机号" @blur="changeFrom">
      <template #left-icon>
        <img src="@/assets/imgs/my/phone_number.png" alt="" width="16" height="16" />
      </template>
    </van-field>
    <van-field is-link v-model="pageInfo.email" label="邮箱" placeholder="请填写邮箱" @blur="changeFrom">
      <template #left-icon>
        <img src="@/assets/imgs/my/email.png" alt="" width="16" height="16" />
      </template>
    </van-field>
    <van-field is-link v-model="pageInfo.address" label="地址" placeholder="请输填写你的地址" @blur="changeFrom">
      <template #left-icon>
        <img src="@/assets/imgs/my/address.png" alt="" width="16" height="16" />
      </template>
    </van-field>
  </div>
</template>

<script>
import { updateUser, getUser } from '@/apis/user'
export default {
  data() {
    return {
      pageInfo: {}
    }
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      getUser().then((r) => {
        this.pageInfo = r.data.data
      })
    },
    changeFrom() {
      updateUser(this.pageInfo).then(() => {
        this.$notify({ type: 'success', message: '修改成功' })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.info {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }

  /deep/ .van-field__label {
    line-height: 20px;
    margin-left: 10px;
    span {
      font-size: 14px;
    }
  }
}
</style>
