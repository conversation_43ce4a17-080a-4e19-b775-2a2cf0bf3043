<template>
  <div class="myInformation">
    <van-sticky>
      <van-nav-bar title="充值记录" left-arrow @click-left="$router.push({ name: 'My' })" />
      <div>
        <div class="time">
          <img src="@/assets/imgs/my/right_arrow.png" alt="" />
          <van-cell>
            <template #title>
              <img src="@/assets/imgs/my/data_icon.png" alt="" />
              <span @click="showCalendar = true">{{ start_at }}</span>
              <van-icon name="arrow" />
            </template>
            <template #default>
              <img src="@/assets/imgs/my/data_icon.png" alt="" />
              <span @click="showCalendar1 = true">{{ end_at }}</span>
              <van-icon name="arrow" />
            </template>
          </van-cell>
          <van-field
            v-model="value"
            label="状态筛选"
            right-icon="arrow"
            placeholder="请选择充值状态"
            @click="showPicker = true"
            disabled
          >
            <template #left-icon>
              <img src="@/assets/imgs/my/type.png" alt="" />
            </template>
          </van-field>
          <van-popup v-model="showPicker" position="bottom">
            <van-picker show-toolbar :columns="columns" @confirm="onConfirm2" @cancel="showPicker = false" />
          </van-popup>
          <van-calendar
            v-model="showCalendar"
            @confirm="onConfirm"
            color="#FF5527"
            :min-date="minDate"
            :max-date="maxDate"
          />
          <van-calendar
            v-model="showCalendar1"
            @confirm="onConfirm1"
            color="#FF5527"
            :min-date="minDate"
            :max-date="maxDate"
          />
        </div>
      </div>
    </van-sticky>
    <ul class="main-box">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多啦" @load="onLoad">
        <li class="item" v-for="(item, index) in pageData" :key="index">
          <van-cell>
            <template #title>
              <span class="item-money">￥{{ item.amount }}</span>
            </template>
            <template #right-icon>
              <span class="item-status_green" v-if="item.status === 1">已支付</span>
              <span class="item-status_yellow" v-if="item.status === 2">已到账</span>
              <span class="item-status_red" v-if="item.status === 3">已退回</span>
              <div class="unpaid_btn" v-if="item.status === 4">
                <span class="item-status_red">待支付</span>
                <van-button style="margin-left: 10px" type="warning" size="mini" @click="handleContinuePayment(item)"
                  >继续支付</van-button
                >
              </div>
            </template>
          </van-cell>
          <van-field label="流水号" :value="item.order_no" disabled />
          <van-field label="充值类型" :value="item.operated.name" disabled />
          <van-field label="充值时间" :value="item.apply_at" disabled />
        </li>
      </van-list>
    </ul>
  </div>
</template>

<script>
import { payments } from '@/apis/user'
import dayjs from 'dayjs'

export default {
  data() {
    return {
      minDate: new Date(2010, 0, 1),
      maxDate: new Date(2050, 0, 31),
      value: '',
      showCalendar: false,
      showCalendar1: false,
      start_at: '起始时间',
      end_at: '结束时间',
      filterStatus: 0,
      showPicker: false,
      columns: [
        { text: '已支付', value: 1 },
        { text: '已到账', value: 2 },
        { text: '已退回', value: 3 }
      ],
      pageData: [],
      loading: false,
      finished: false,
      paging: {
        current: 0,
        total: 0
      },
      status: [
        { label: '已支付', value: 1 },
        { label: '已到账', value: 2 },
        { label: '已退回', value: 3 }
      ]
    }
  },
  watch: {
    start_at(newN) {
      const _data = {
        starts_at: newN,
        ends_at: this.end_at === '结束时间' ? '' : this.end_at,
        status: this.filterStatus ? this.filterStatus : ''
      }

      this.pageData = []
      this.paging = {
        current: 1,
        total: 0
      }

      this.getPageData({ filter: _data })
    },

    end_at(newN) {
      const _data = {
        starts_at: this.start_at,
        ends_at: this.end_at === '结束时间' ? '' : this.end_at,
        status: this.filterStatus ? this.filterStatus : ''
      }

      this.pageData = []
      this.paging = {
        current: 1,
        total: 0
      }

      this.getPageData({ filter: _data })
    }
  },
  methods: {
    onLoad() {
      this.paging.current++
      this.getPageData()
    },
    onConfirm(date) {
      this.start_at = dayjs(date).format('YYYY-MM-DD').toString()
      this.showCalendar = false
    },
    onConfirm1(date) {
      this.end_at = dayjs(date).format('YYYY-MM-DD').toString()
      this.showCalendar1 = false
    },
    onConfirm2(value) {
      this.value = value.text
      this.filterStatus = value.value
      this.showPicker = false
      const _data = {
        starts_at: this.start_at === '起始时间' ? '' : this.start_at,
        ends_at: this.end_at === '结束时间' ? '' : this.end_at,
        status: this.filterStatus ? this.filterStatus : ''
      }

      this.pageData = []
      this.paging = {
        current: 1,
        total: 0
      }

      this.getPageData({ filter: _data })
    },
    getPageData(data = {}) {
      payments(Object.assign({}, data, { page: this.paging.current })).then((r) => {
        this.pageData.push(...r.data.data)

        this.loading = false
        this.paging.current = r.data.meta.current_page
        this.paging.total = r.data.meta.total
        this.paging.last_page = r.data.meta.last_page

        if (this.paging.current >= r.data.meta.last_page) {
          this.finished = true
        }
      })
    },
    handleContinuePayment(row) {
      if (row.callback_data) {
        window.location.href = row.callback_data.payment_url
      } else {
        this.$message.warning('没有找到有效的支付链接')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.myInformation {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .main-box {
    padding: 15px;
    .item {
      width: 345px;
      // height: 141px;
      background: #ffffff;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      margin-bottom: 15px;
      .item-money {
        color: #333;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
}
.item-status_green {
  color: #5cc374;
  font-size: 14px;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    vertical-align: middle;
    border-radius: 50%;
    background-color: #5cc374;
    margin-right: 5px;
  }
}
.item-status_yellow {
  color: #f9ae1e;
  font-size: 14px;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    vertical-align: middle;
    border-radius: 50%;
    background-color: #f9ae1e;
    margin-right: 5px;
  }
}
.item-status_red {
  color: red;
  font-size: 14px;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    vertical-align: middle;
    border-radius: 50%;
    background-color: red;
    margin-right: 5px;
  }
}
//
.van-cell__title,
.van-cell__value {
  display: flex;
  align-items: center;

  img {
    width: 13px;
    height: 13px;
    background-size: contain;
  }
  span {
    display: inline-block;
    color: #333;
    font-size: 14px;
    margin-left: 5px;
    width: 100px;
  }
  .van-icon {
    font-size: 12px;
  }
}
.van-sticky {
  .van-cell__value.van-cell__value {
    justify-content: flex-end;
    span {
      text-align: left;
    }
  }
}

.time {
  position: relative;
  & > img {
    width: 25px;
    height: 5px;
    position: absolute;
    top: 23%;
    left: 50%;
    transform: translate(-50%);
    z-index: 10;
  }
}
.van-field {
  img {
    width: 13px;
    height: 13px;
    margin-right: 3px;
  }
}
.unpaid_btn {
  display: flex;
}
</style>
