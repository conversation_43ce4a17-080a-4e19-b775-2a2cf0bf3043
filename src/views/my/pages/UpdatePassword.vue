<template>
  <div class="myInformation">
    <van-sticky>
      <van-nav-bar title="修改密码" left-arrow @click-left="$router.push({ name: 'My' })" />
    </van-sticky>
    <van-field v-model="form.old_password" label="原密码" left-icon="lock" placeholder="请填写原密码" />
    <van-field v-model="form.password" label="新密码" left-icon="lock" placeholder="请填写新密码" />
    <van-field
      v-model="form.password_confirmation"
      label="确认新密码"
      left-icon="lock"
      placeholder="请填写确认新密码"
    />
    <div class="btn">
      <van-button type="primary" block :color="primaryColor" @click="edit">确认修改</van-button>
    </div>
  </div>
</template>

<script>
import { editPassword } from '@/apis/user'

export default {
  data() {
    return {
      form: {
        old_password: '',
        password: '',
        password_confirmation: ''
      }
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  methods: {
    edit() {
      for (const i in this.form) {
        if (this.form[i] === '') {
          this.$notify({ type: 'warning', message: '所有项都为必填项' })
          return
        }
        if (this.form[i].length < 6) {
          this.$notify({ type: 'warning', message: '密码必须大于5位数' })
          return
        }
      }
      editPassword(this.form).then(() => {
        this.$notify({ type: 'warning', message: '修改成功,请重新登陆' })
        this.$router.push('/login')
      })
    }
  }
}
</script>

<style lang="less" scoped>
.myInformation {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .btn {
    padding: 25px 15px;
  }
}
</style>
