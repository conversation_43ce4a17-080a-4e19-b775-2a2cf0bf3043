<template>
  <div class="message">
    <van-sticky>
      <van-nav-bar title="消息" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <template v-if="messageList.length > 0">
      <ul class="massage-box">
        <li
          class="item"
          :class="{ hasreade: item.is_have_read === 1 }"
          v-for="(item, index) in messageList"
          :key="index"
        >
          <div class="message-body">
            <div>{{ item.message }}</div>
            <div>{{ item.created_at | date }}</div>
          </div>
          <div class="message-operation">
            <span class="status" v-if="item.is_have_read === 0">{{ item.is_have_read ? '已读' : '未读' }}</span>
          </div>
        </li>
      </ul>
    </template>
    <van-empty v-else description="暂无消息" />
  </div>
</template>

<script>
import { messageList, markMessageAsRead } from '@/apis/user'
import dayjs from 'dayjs'

export default {
  data() {
    return {
      messageList: []
    }
  },
  filters: {
    date(value) {
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  mounted() {
    messageList().then((r) => {
      this.messageList = r.data.data

      this.messageList
        .filter((m) => !m.is_have_read)
        .forEach((message) => {
          markMessageAsRead(message.id)
        })
    })
  }
}
</script>

<style lang="less" scoped>
.message {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .massage-box {
    padding: 15px;
    .item {
      padding: 15px;
      background: #ffffff;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      & > .message-body {
        flex: 1;
        div:first-child {
          color: #333333;
          font-size: 13px;
        }
        div:last-child {
          color: #999;
          font-size: 13px;
          margin-top: 6px;
        }
      }
      & > .message-operation {
        text-align: center;
        color: #f82e2e;
        width: 40px;
        vertical-align: middle;
        position: relative;
        .status {
          &::before {
            display: inline-block;
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #f82e2e;
            vertical-align: middle;
            margin-right: 4px;
          }
        }
      }
    }
  }
}
.hasreade {
  color: #999999 !important;
  background-color: #f6f6f6 !important;
  span::before {
    background-color: #999999 !important;
  }
}
</style>
