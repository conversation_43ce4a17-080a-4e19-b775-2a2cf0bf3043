<template>
  <div style="min-height: 100vh; background-color: #fafafa">
    <van-sticky>
      <van-nav-bar title="我的客户" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>

    <section class="users">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多啦" @load="onLoad">
        <van-cell-group v-for="user in users" :key="user.id" inset class="__cell_group">
          <van-cell>
            <template #title>
              <van-cell class="__label">
                <template>
                  <span class="__title">{{ user.name }}</span>
                  <span class="__title" style="color: #666666; float: right">{{
                    user.type === 1 ? '个人用户' : '企业用户'
                  }}</span>
                </template>
              </van-cell>
            </template>
            <template #label>
              <ul class="__label">
                <li>
                  <div class="__label_t">联系方式</div>
                  <div class="__label_c">{{ user.phone_number }}</div>
                </li>
                <li>
                  <div class="__label_t">账户余额</div>
                  <div class="__label_c">{{ user.balance }}</div>
                </li>
                <li>
                  <div class="__label_t">注册时间</div>
                  <div class="__label_c">{{ user.created_at }}</div>
                </li>

                <li class="__btn" style="float: right">
                  <van-button
                    :color="primaryColor"
                    icon="manager-o"
                    size="mini"
                    type="primary"
                    @click="
                      () => {
                        showUserPopup = true
                        currentUser = user
                      }
                    "
                    >修改</van-button
                  >
                  <van-button
                    :color="primaryColor"
                    icon="balance-list-o"
                    size="mini"
                    type="primary"
                    style="margin-left: 25px"
                    @click="
                      () => {
                        showChargePopup = true
                        currentUser = user
                      }
                    "
                    >充值</van-button
                  >
                </li>
              </ul>
            </template>
          </van-cell>
        </van-cell-group>
      </van-list>
    </section>

    <van-popup v-model="showChargePopup" position="bottom">
      <div class="popup-wrap">
        <h3>充值</h3>
        <p>充值金额(元):</p>
        <van-field
          label-width="1em"
          class="__has-bottom-border"
          v-model="chargeAmount"
          name="chargeAmount"
          label="¥"
          placeholder="0.00"
          :rules="[{ required: true, message: '请输入充值金额' }]"
        />
        <van-button block :color="primaryColor" @click="handleCharge">充值</van-button>
        <van-button
          plain
          block
          :color="primaryColor"
          style="margin-top: 10px"
          @click="
            () => {
              showChargePopup = false
              chargeAmount = void 0
            }
          "
          >取消</van-button
        >
      </div>
    </van-popup>

    <van-popup v-model="showUserPopup" position="bottom">
      <div class="popup-wrap">
        <van-form @submit="handleUpdateUser">
          <van-field name="radio" label="用户类型" required>
            <template #input>
              <van-radio-group v-model="currentUser.type" direction="horizontal">
                <van-radio :name="1">个人用户</van-radio>
                <van-radio :name="2">企业用户</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-model="currentUser.username"
            placeholder="请输入用户名"
            label="用户名"
            type="username"
            required
            :rules="[{ required: true, message: '请输入用户名' }]"
          />
          <van-field
            v-model="currentUser.name"
            placeholder="请输入客户名称"
            label="客户名称"
            type="name"
            required
            :rules="[{ required: true, message: '请输入客户名称' }]"
          />
          <van-field
            v-model="currentUser.idcard_no"
            :placeholder="currentUser.type === 1 ? '请输入身份证号' : '请输入营业执照号'"
            :label="currentUser.type === 1 ? '身份证号' : '营业执照号'"
            type="name"
            required
            :rules="[{ required: true, message: '请输入身份证号或营业执照号' }]"
          />
          <van-field
            v-model="currentUser.phone_number"
            placeholder="请输入手机号"
            label="手机号"
            type="phone_number"
            required
            :rules="[{ required: true, message: '请输入手机号' }]"
          />
          <van-field
            v-model="currentUser.email"
            placeholder="请输入邮箱"
            label="邮箱"
            type="email"
            required
            :rules="[{ required: true, message: '请输入邮箱' }]"
          />
          <van-field
            v-model="currentUser.password"
            placeholder="请输入密码"
            label="密码"
            type="password"
            :rules="[{ min: 5, max: 32, message: '请输入正确的密码' }]"
          />

          <div style="margin-top: 10px">
            <van-button block :color="primaryColor" native-type="submit">修改</van-button>
            <van-button
              plain
              block
              :color="primaryColor"
              native-type="reset"
              style="margin-top: 10px"
              @click="
                () => {
                  showUserPopup = false
                  chargeAmount = void 0
                }
              "
              >取消</van-button
            >
          </div>
        </van-form>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getUsers, charge, updateMyUser } from '@/apis/user'

export default {
  data() {
    return {
      users: [],
      loading: false,
      finished: false,
      paging: {
        current: 0,
        total: 0
      },
      currentUser: {},
      showChargePopup: false,
      chargeAmount: void 0,
      showUserPopup: false
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  methods: {
    handleUpdateUser() {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      updateMyUser(this.currentUser.id, this.currentUser).then(() => {
        loading.clear()
        this.$toast.success('修改成功')
        this.showUserPopup = false
        this.currentUser = {}

        this.fetchUsers()
      })
    },
    handleCharge() {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      charge(this.currentUser.id, { amount: this.chargeAmount })
        .then(() => {
          loading.clear()
          this.$toast.success('充值成功')
          this.showChargePopup = false
          this.chargeAmount = void 0
        })
        .catch(() => {
          this.$toast.fail('充值失败')
        })
    },
    onLoad() {
      this.paging.current++

      this.fetchUsers()
    },
    fetchUsers() {
      getUsers({ page: this.paging.current_page }).then((r) => {
        this.users = r.data.data

        this.loading = false
        this.paging.current = r.data.meta.current_page
        this.paging.total = r.data.meta.total
        this.paging.last_page = r.data.meta.last_page

        if (this.paging.current >= r.data.meta.last_page) {
          this.finished = true
        }
      })
    }
  }
}
</script>

<style lang="less">
.users {
  margin-top: 15px;
  .__cell_group {
    margin-top: 15px;
    box-shadow: 4px 4px 20px 0px rgba(0, 0, 0, 0.05);
  }

  .__title {
    padding: 0 0 10px 0;
  }

  .__label {
    padding: 0;
    margin: 0;
    li {
      padding: 5px 0;
      font-size: 14px;

      div {
        height: auto;
        display: inline-block;
      }

      .__label_t {
        min-width: 80px;
      }

      .__label_c {
        max-width: calc(100% - 80px);
      }

      .passed {
        color: #4caf50;
      }

      .reject {
        color: #ff7f4b;
      }
    }
  }
}

.popup-wrap {
  padding: 25px;
  font-size: 14px;

  h3 {
    font-size: 18px;
  }

  .__has-bottom-border {
    margin-bottom: 10px;
    border-bottom: 1px solid #999;
  }
}
</style>
