<template>
  <div class="my">
    <div class="bg-img">
      <img src="@/assets/imgs/my/bg_1.png" alt="" />
    </div>
    <div class="username">{{ username }}</div>
    <div class="middle-bar">
      <div class="middle-box">
        <van-row>
          <van-col :span="12">
            <div>￥{{ money }}</div>
            <div class="balance_box">
              <span>余额</span>
              <van-button
                v-if="onlinePaymentIsEnabled == 1"
                :color="primaryColor"
                type="warning"
                size="mini"
                @click="onlineRechargeDialogVisible = true"
                >
                <span style="color: #fff">在线充值</span>
                </van-button>
              <van-dialog
                v-model="onlineRechargeDialogVisible"
                @confirm="recharge"
                @cancel="onlineRechargeamount = ''"
                title="请填写金额"
                show-cancel-button
              >
                <van-field
                  v-model="onlineRechargeamount"
                  name="金额"
                  type="number"
                  placeholder="金额"
                  :rules="[{ required: true, message: '请填写金额' }]"
                />
              </van-dialog>
            </div>
          </van-col>
          <van-col :span="12" @click="$router.push({ name: 'Messages' })">
            <div>{{ message }}条</div>
            <div>消息</div>
          </van-col>
        </van-row>
      </div>
    </div>
    <div class="list-box">
      <van-cell title="我的资料" is-link :to="{ name: 'Profile' }">
        <template #icon> <img src="@/assets/imgs/my/profile.png" width="21" height="21" alt="" /> </template>
      </van-cell>
      <van-cell title="充值记录" is-link :to="{ name: 'ChargeRecords' }">
        <template #icon> <img src="@/assets/imgs/my/charge-records.png" width="21" height="21" alt="" /> </template>
      </van-cell>
      <van-cell title="修改密码" is-link :to="{ name: 'UpdatePassword' }">
        <template #icon> <img src="@/assets/imgs/my/password.png" width="21" height="21" alt="" /> </template>
      </van-cell>
      <van-cell
        title="我的客户"
        is-link
        :to="{ name: 'MyUsers' }"
        v-if="user.is_agent && features?.includes('customers')"
      >
        <template #icon> <img src="@/assets/imgs/my/user.png" width="20" height="17" alt="" /> </template>
      </van-cell>
    </div>
    <div class="logout">
      <van-button type="primary" color="#ffffff" block @click="logout">退出登录</van-button>
    </div>
  </div>
</template>

<script>
import { getHomePageShowData } from '@/apis/homePage'
import { logout, onlineRecharge } from '@/apis/user'
import { userKey } from '@/config'
import { mapGetters } from 'vuex'

export default {
  name: 'my',
  data() {
    return {
      username: '',
      money: '',
      message: '',
      onlineRechargeDialogVisible: false,
      onlineRechargeamount: ''
    }
  },
  computed: {
    ...mapGetters(['features']),
    user() {
      return JSON.parse(localStorage.getItem(userKey))
    },
    primaryColor() {
      return this.$store.state.primaryColor
    },
    onlinePaymentIsEnabled() {
      return this.$store.state.onlinePaymentIsEnabled
    }
  },
  mounted() {
    this.getPageData()
  },
  methods: {
    getPageData() {
      getHomePageShowData().then((r) => {
        const _temp = r.data.data
        this.username = _temp.user.name
        this.money = _temp.user.balance
        this.message = _temp.messages?.length
      })
    },
    logout() {
      logout().then((r) => {
        this.$notify({ type: 'warning', message: '登出成功' })
        this.$router.push('/login')
        localStorage.clear()
      })
    },
    recharge() {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      onlineRecharge({ amount: this.onlineRechargeamount }).then((r) => {
        this.$toast.success('已提交')
        if (r.data.payment_url) {
          window.location.href = r.data.payment_url
          return
        }
        loading.clear()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.my {
  min-height: 100vh;
  background-color: #ededed;
  .bg-img {
    width: 100%;
    overflow: hidden;
    img {
      width: 100%;
    }
  }
  .username {
    font-size: 23px;
    font-weight: bold;
    color: #fbfbfb;
    position: absolute;
    top: 73px;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .middle-bar {
    position: absolute;
    right: 15px;
    left: 15px;
    top: 152px;
    .middle-box {
      height: 76px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 4px 4px 15px 0px rgba(0, 0, 0, 0.08);
      .van-row {
        padding: 21px 0 20px;
        .van-col {
          text-align: center;
          height: 100%;
          &:first-child {
            border-right: 1px solid #e6e6e6;
          }
          div {
            &:first-child {
              color: #333;
              font-size: 19px;
              font-weight: bold;
            }
            &:last-child {
              color: #999;
              font-size: 14px;
              font-weight: 400;
            }
          }
        }
      }
    }
  }
  .list-box {
    padding: 78px 0 0;
    /deep/ .van-cell__title {
      margin-left: 15px;
      span {
        font-size: 15px !important;
      }
    }
  }
  .logout {
    margin-top: 20px;
  }
}
.balance_box {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
// 退出按钮字体颜色
.van-button__text {
  color: #333;
}
</style>
