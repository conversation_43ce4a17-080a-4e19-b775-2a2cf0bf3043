<template>
  <div class="package-detail">
    <van-sticky>
      <van-nav-bar title="套餐详情" left-arrow @click-left="$router.go(-1)" />
      <header>
        <div class="left-box">
          <div>{{ pageData.name }}</div>
          <div>{{ pageData.label }}</div>
        </div>
        <div class="right-box">
          <img src="@/assets/imgs/insure/logo.png" alt="" />
        </div>
      </header>
    </van-sticky>
    <ul class="card-options">
      <li class="card-option">
        <van-field disabled>
          <template #label>
            <img class="header-img" src="@/assets/imgs/insure/qy.png" alt="" />
            <span class="header-title">保障权益</span>
          </template>
        </van-field>
        <van-field
          disabled
          :label="item.name"
          :value="`${item.amount}`"
          v-for="item in pageData.benefit"
          :key="item.id"
        >
          <template #button>
            <span>{{ item.content }}</span>
          </template>
        </van-field>
      </li>
      <li class="card-option">
        <van-field disabled>
          <template #label>
            <img class="header-img" src="@/assets/imgs/insure/mp.png" alt="" />
            <span class="header-title">免赔</span>
          </template>
        </van-field>
        <div class="word">{{ pageData.deductible }}</div>
      </li>
      <li class="card-option">
        <van-field disabled>
          <template #label>
            <img class="header-img" src="@/assets/imgs/insure/yd.png" alt="" />
            <span class="header-title">特别约定</span>
          </template>
        </van-field>
        <div class="word">
          {{ pageData.special_agreement }}
        </div>
      </li>
      <li class="card-option">
        <van-field disabled>
          <template #label>
            <img class="header-img" src="@/assets/imgs/insure/xz.png" alt="" />
            <span class="header-title">投保须知</span>
          </template>
        </van-field>
        <div class="word" v-html="pageData.notice"></div>
      </li>
    </ul>
    <van-button type="primary" block :color="primaryColor" @click="ljtb">立即投保</van-button>
  </div>
</template>

<script>
import { productDetail } from '@/apis/policy'
import { Dialog } from 'vant'
import { userKey } from '@/config'

export default {
  data() {
    return {
      pageId: '',
      pageData: {}
    }
  },
  computed: {
    user() {
      return JSON.parse(localStorage.getItem(userKey))
    },
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  mounted() {
    this.pageId = this.$route.params.id
    this.getPageData()
  },
  methods: {
    // 本页数据
    getPageData() {
      productDetail(this.pageId).then((r) => {
        r.data.data.benefit = JSON.parse(r.data.data.benefit)
        this.pageData = r.data.data
      })
    },
    // 跳转到立即投保
    ljtb() {
      if (this?.pageData?.channel?.platform?.includes(this.user.platform_id) && this?.pageData?.inform?.length > 1) {
        Dialog.confirm({
          title: '投保告知',
          message: this?.pageData?.inform,
          messageAlign: 'left',
          allowHtml: true,
          theme: 'round-button'
        }).then(() => {
          this.$router.push({ name: 'InsureOtherForm', params: { id: this.pageId } })
        })
      } else {
        this.$router.push({ name: 'InsureOtherForm', params: { id: this.pageId } })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.package-detail {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  header {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 15px;
    .left-box {
      div:first-child {
        color: #111;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      div:last-child {
        color: #999;
        font-size: 13px;
      }
    }
    .right-box {
      width: 63px;
      height: 40px;
      background-color: #eee;
      display: flex;
      align-items: center;
      img {
        width: 100%;
      }
    }
  }
  .card-options {
    padding: 10px 15px;
    .card-option {
      background-color: #fff;
      border-radius: 4px;
      margin-bottom: 10px;
      .van-field {
        /deep/ .van-field__button,
        /deep/ .van-field__control,
        /deep/ .van-cell__title {
          color: #333;
        }
        .header-img {
          width: 14px;
          vertical-align: middle;
          margin-right: 8px;
        }
        .header-title {
          color: #111111;
          font-size: 16px;
        }
      }

      .word {
        padding: 10px 15px;
        color: #111;
        font-size: 13px;
      }
      /deep/ .van-field__control:disabled {
        -webkit-text-fill-color: #111;
      }
    }
  }
}
.van-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
