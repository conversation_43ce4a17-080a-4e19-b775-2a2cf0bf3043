<template>
  <div class="insured-immediately">
    <van-sticky>
      <van-nav-bar title="保单填写" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-form @submit="onSubmit">
      <!-- 投保人信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">投保人信息</span>
          </template>
        </van-cell>
        <van-field
          v-model="form.applicant_contact_name"
          name="姓名"
          placeholder="请填写姓名"
          :rules="[{ required: true, message: '请填写姓名' }]"
        >
          <template #label>
            <span class="required">姓名</span>
          </template>
        </van-field>
        <van-field
          v-model="form.applicant_contact_phone"
          :rules="[{ required: true, message: '请填写姓名' }]"
          placeholder="请填写联系方式"
        >
          <template #label>
            <span class="required">联系方式</span>
          </template>
        </van-field>
      </div>
      <!-- 单位信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">基本信息</span>
          </template>
        </van-cell>

        <van-field
          v-model="form.policyholder"
          placeholder="请填写投保人"
          :rules="[{ required: true, message: '需填写投保人' }]"
        >
          <template #label>
            <span class="required">投保人</span>
          </template>
        </van-field>
        <van-field
          v-model="form.insured"
          name="被保人"
          placeholder="请填写被保人"
          :rules="[{ required: true, message: '需填写被保人' }]"
        >
          <template #label>
            <span class="required">被保人</span>
          </template>
        </van-field>
        <van-field
          readonly
          clickable
          name="datetimePicker"
          v-model="form.start_at"
          placeholder="点击选择时间"
          @click="showStartsAtCalendar = true"
        >
          <template #label>
            <span class="required">投保日期</span>
          </template>
        </van-field>
        <van-calendar :color="primaryColor" title="投保日期" v-model="showStartsAtCalendar" @confirm="confirmStatsAt" />
        <van-field
          readonly
          clickable
          name="datetimePicker"
          v-model="form.end_at"
          placeholder="点击选择时间"
          @click="showEndsAtCalendar = true"
        >
          <template #label>
            <span class="required">终保日期</span>
          </template>
        </van-field>
        <van-calendar :color="primaryColor" title="终保日期" v-model="showEndsAtCalendar" @confirm="confirmEndsAt" />
      </div>
      <!-- 保单相关信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">保单相关信息</span>
          </template>
        </van-cell>
        <div v-for="item in customComponent" :key="item.id">
          <!-- 单行文本 -->
          <van-field
            v-model="form[item.name]"
            name="投保人证件号"
            placeholder="必填项"
            v-if="item.type === 'text'"
            :rules="[{ required: true, message: '必填项' }]"
          >
            <template #label>
              <span class="required">{{ item.title }}</span>
            </template>
          </van-field>
          <!-- 多行文本 -->
          <van-field
            v-model="form[item.name]"
            rows="2"
            autosize
            type="textarea"
            maxlength="50"
            v-if="item.type === 'textarea'"
            placeholder="请输入留言"
            show-word-limit
          >
            <template #label>
              <span class="required">{{ item.title }}</span>
            </template>
          </van-field>
          <!-- 上传文件 -->
          <van-field name="uploader" v-if="item.type === 'file'">
            <template :slot-scope="item" slot="input">
              <van-uploader v-model="form[item.name]" :max-count="1" :max-size="4 * 1024 * 1024" />
            </template>
            <template :slot-scope="item" slot="label">
              <span class="required">{{ item.title }}</span>
            </template>
          </van-field>
        </div>
      </div>
      <!-- 发票相关信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">发票相关信息</span>
          </template>
        </van-cell>
        <van-field
          readonly
          clickable
          :value="form.invoice_type"
          label="发票类型"
          placeholder="点击选择发票类型"
          @click="showInvoiceType = true"
        />
        <van-popup v-model="showInvoiceType" position="bottom">
          <van-picker
            show-toolbar
            :columns="columns"
            @confirm="confirmInvoiceTypeChange"
            @cancel="showInvoiceType = false"
          />
        </van-popup>
        <div class="invoice" v-if="is_show">
          <template>
            <van-field
              v-model="form.title"
              placeholder="请填写发票抬头"
              :rules="[{ required: true, message: '需填写发票抬头' }]"
            >
              <template #label>
                <span class="required">发票抬头</span>
              </template>
            </van-field>
            <van-field
              v-model="form.tax_no"
              placeholder="请填写纳税人识别号"
              :rules="[{ required: true, message: '需填写纳税人识别号' }]"
            >
              <template #label>
                <span class="required">纳税人识别号</span>
              </template>
            </van-field>
          </template>
          <!-- emmm.... van-picker 真难用 -->
          <template v-if="form.invoice_type === '专票'">
            <van-field
              v-model="form.bank"
              placeholder="请填写开户行"
              :rules="[{ required: true, message: '需填写开户行' }]"
            >
              <template #label>
                <span class="required">开户行</span>
              </template>
            </van-field>
            <van-field
              v-model="form.card_no"
              placeholder="请填写账号"
              :rules="[{ required: true, message: '需填写账号' }]"
            >
              <template #label>
                <span class="required">账号</span>
              </template>
            </van-field>
            <van-field
              v-model="form.company_address"
              placeholder="请填写公司地址"
              :rules="[{ required: true, message: '需填写公司地址' }]"
            >
              <template #label>
                <span class="required">公司地址</span>
              </template>
            </van-field>
            <van-field
              v-model="form.company_phone"
              placeholder="请填写公司电话"
              :rules="[{ required: true, message: '需填写公司电话' }]"
            >
              <template #label>
                <span class="required">公司电话</span>
              </template>
            </van-field>
          </template>
        </div>
        <van-cell>
          <template #title>
            <van-checkbox v-model="form.check" :checked-color="primaryColor">
              我已详细阅读<span class="word_color" @click="showAgreement = true">投保须知及特别约定</span>的内容
            </van-checkbox>

            <van-popup
              v-model="showAgreement"
              position="bottom"
              :style="{ height: '50%', padding: '10px', BoxSizing: 'border-box' }"
              round
              closeable
            >
              <h3>投保须知</h3>
              <div>
                投保前请您仔细阅读:
                <a :href="product?.clause_file">产品条款</a>
                <a :href="f.file" v-for="f in JSON.parse(product?.related_file || '[]')" :key="f.id">
                  {{ f.name }}&nbsp;
                </a>
              </div>
              <div v-html="product?.notice"></div>
              <h3>特别约定</h3>
              <div v-html="product?.special_agreement"></div>
            </van-popup>
          </template>
        </van-cell>
        <div class="btn-box">
          <van-button block :color="primaryColor" size="small" native-type="submit">提交投保单</van-button>
        </div>
      </div>
    </van-form>
    <div class="item-wrapper"></div>
  </div>
</template>

<script>
import { productDetail, submitFrom, getPolicy, updatePolicy } from '@/apis/policy'
import dayjs from 'dayjs'

export default {
  name: 'InsureOtherForm',
  data() {
    return {
      columns: [
        { text: '不开发票', value: 'no' },
        { text: '普票', value: 'plain' },
        { text: '专票', value: 'special' }
      ],
      showStartsAtCalendar: false,
      showEndsAtCalendar: false,
      showInvoiceType: false,
      form: {
        invoice_type: '不开发票'
      },
      is_show: false,
      product: {},
      showAgreement: false,
      customComponent: [],
      policy: {}
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  watch: {
    'form.invoice_type': {
      handler(newN, oldN) {
        if (newN !== oldN) {
          if (newN === '不开发票' || newN === '') {
            this.is_show = false
          } else {
            this.is_show = true
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getCustomComponent()
  },
  methods: {
    getCustomComponent() {
      productDetail(this.$route.params.id).then((r) => {
        this.product = r.data.data
        this.customComponent = r.data.data.model.fields

        // initialize fields.
        this.customComponent.forEach((cp) => {
          if (cp.type === 'file') {
            this.$set(this.form, cp.name, [])
          } else {
            this.$set(this.form, cp.name, '')
          }
        })

        if (this.$route.query.policy_id) {
          getPolicy(this.$route.query.policy_id).then((r) => {
            this.policy = r.data.data

            this.$set(this.form, 'applicant_contact_name', this.policy?.detail?.applicant_contact_name)
            this.$set(this.form, 'applicant_contact_phone', this.policy?.detail?.applicant_contact_phone)
            this.$set(this.form, 'insured', this.policy.insured)
            this.$set(this.form, 'policyholder', this.policy.policyholder)
            this.$set(this.form, 'start_at', this.policy?.detail?.start_at)
            this.$set(this.form, 'end_at', this.policy?.detail?.end_at)

            const additionalData = JSON.parse(this.policy?.detail?.addition)
            additionalData.forEach((e) => {
              if (e.type === 'file') {
                this.form[e.name] = [
                  {
                    url: e.value
                  }
                ]
              } else {
                this.form[e.name] = e.value
              }
            })

            const invoiceContent = JSON.parse(this.policy?.detail?.invoice_content) || []
            Object.keys(invoiceContent).forEach((k) => {
              if (k === 'invoice_type') {
                this.form.invoice_type = this.columns.find((c) => c.value === invoiceContent[k]).text
              } else {
                this.form[k] = invoiceContent[k]
              }
            })

            this.form.check = true
            this.form.policy_id = this.$route.query.policy_id
          })
        }
      })
    },
    onClickLeft() {
      this.$router.go(-1)
    },
    // 起保时间
    confirmStatsAt(date) {
      this.form.start_at = dayjs(date).format('YYYY-MM-DD').toString()
      this.form.end_at = dayjs(this.form.start_at)
        .add(this.product?.insurance_period, 'month')
        .format('YYYY-MM-DD')
        .toString()
      this.showStartsAtCalendar = false
    },
    // 终保时间
    confirmEndsAt(date) {
      this.form.end_at = dayjs(date).format('YYYY-MM-DD').toString()
      this.showEndsAtCalendar = false
    },
    onSubmit() {
      const _temp = Object.assign({}, this.form, { type: 4 }, { product_id: this.$route.params.id })
      _temp.invoice_type = this.columns.find((item) => item.text === _temp.invoice_type).value

      // 转换自定义字段文件部分
      for (const k in _temp) {
        if (_temp[k] instanceof Array) {
          if (_temp[k][0].file !== undefined) {
            _temp[k] = _temp[k][0].file
          } else {
            _temp[k] = _temp[k][0].url
          }
        }
      }

      if (this.$route.query.from === 'edit') {
        updatePolicy(_temp.policy_id, _temp).then(() => {
          this.$router.push({ name: 'OtherDetail', params: { id: _temp.policy_id } })
        })
      } else {
        submitFrom(_temp).then((r) => {
          this.$router.push({
            name: 'InsureOtherPay',
            params: { id: r.data.data.id },
            query: { product_id: this.product.id }
          })
        })
      }
    },
    confirmInvoiceTypeChange(value) {
      this.form.invoice_type = value.text
      this.showInvoiceType = false
    }
  }
}
</script>

<style lang="less" scoped>
.insured-immediately {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
  }
}
// 必填项
.required::after {
  content: '*';
  color: red;
}
// 保单填写
.van-cell {
  img {
    width: 12px;
    height: 15px;
    background-size: contain;
    line-height: 15px;
  }
  .word {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-left: 5px;
  }
}
// 左侧内容label宽度
/deep/ .van-cell__title.van-field__label {
  width: 120px;
}
// 上传文件位置
/deep/ .van-field__control.van-field__control--custom {
  justify-content: flex-end;
}
.word_color {
  color: var(--primary-color);
}
.van-checkbox {
  font-size: 14px;
  color: #333;
}
.btn-box {
  padding: 10px 15px;
  .van-button {
    margin-bottom: 10px;
  }
}
</style>
