<template>
  <div class="employerPage">
    <van-sticky>
      <van-nav-bar title="产品选择" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <div class="wrapper">
      <div class="card-item">
        <div class="titile">
          <img src="@/assets/imgs/insure/choose_5.png" alt="" />
          <p>产品分类</p>
        </div>
        <van-divider />
        <div class="content" id="content" @click="chooseCompany">
          <van-button v-for="(item, index) in productType" :key="index" :data-company="item.id" size="small" round>{{
            item.name
          }}</van-button>
        </div>
      </div>
      <div class="card-item">
        <div class="titile">
          <img src="@/assets/imgs/insure/choose_2.png" alt="" />
          <p>保险公司</p>
        </div>
        <van-divider />
        <div class="content" id="product" @click="chooseProduct">
          <van-button v-for="item in company" :key="item.id" :data-company="item.id" size="small" round>{{
            item.name
          }}</van-button>
        </div>
      </div>
      <div class="card-item none-border">
        <ul class="product-choose">
          <li class="item" v-for="item in productList" :key="item.id">
            <div class="img-box">
              <img :src="item.company.logo" alt="" />
            </div>
            <div class="content-box">
              <div>{{ item.name }}</div>
              <div style="width: 100%">{{ item.label }}</div>
              <div>
                <span>￥{{ item.reference_price }}</span>
                <span @click="$router.push({ name: 'InsureOtherDetail', params: { id: item.id } })">查看详情</span>
              </div>
            </div>
          </li>
        </ul>
        <van-empty v-if="productList.length <= 0" description="暂无产品"></van-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { productType, company, productList } from '@/apis/policy'

export default {
  name: 'employerPage',
  data() {
    return {
      productType: [],
      company: [],
      productList: [],
      chooseoptions: {
        company: '',
        product: '',
        package: ''
      },
      category_id: Number,
      company_id: Number
    }
  },
  mounted() {
    // 获取产品分类
    productType().then((r) => {
      this.productType = r.data.data
    })
    // 获取保险公司
    company().then((r) => {
      this.company = r.data.data
    })
    // 获取列表
    this.getList()
  },
  watch: {
    category_id() {
      const _data = {
        filter: {
          category_id: this.category_id,
          company_id: this.company_id
        }
      }

      this.getList(_data)
    },
    company_id() {
      const _data = {
        filter: {
          category_id: this.category_id,
          company_id: this.company_id
        }
      }
      this.getList(_data)
    }
  },
  methods: {
    getList(_data) {
      productList(_data).then((r) => {
        this.productList = r.data.data
      })
    },
    // 选择的公司
    chooseCompany(e) {
      const items = Array.from(document.getElementById('content').getElementsByClassName('van-button'))
      items.forEach((item) => {
        item.setAttribute('class', 'van-button van-button--default van-button--small van-button--round none-active')
        if (e.target === item) {
          this.category_id = e.target.dataset.company
          e.target.setAttribute('class', 'van-button van-button--default van-button--small van-button--round active')
          this.chooseoptions.company = e.target.dataset.company
        }
      })
    },
    chooseProduct(e) {
      const items = Array.from(document.getElementById('product').getElementsByClassName('van-button'))
      items.forEach((item) => {
        item.setAttribute('class', 'van-button van-button--default van-button--small van-button--round none-active')
        if (e.target === item) {
          this.company_id = e.target.dataset.company
          e.target.setAttribute('class', 'van-button van-button--default van-button--small van-button--round active')
          this.chooseoptions.product = e.target.dataset.company
        }
      })
    },

    choosePackage(e) {
      const items = Array.from(document.getElementById('package').getElementsByClassName('van-button'))
      items.forEach((item) => {
        item.setAttribute('class', 'van-button van-button--default van-button--small van-button--round none-active')
        if (e.target === item) {
          console.log(e.target)
          e.target.setAttribute('class', 'van-button van-button--default van-button--small van-button--round active')
          this.chooseoptions.package = e.target.dataset.company
        }
      })
    },
    // 立即投保
    ohterImmediately() {
      this.$router.push({
        name: 'otherImmediately'
      })
    }
  }
}
</script>

<style lang="less" scoped>
.employerPage {
  min-height: 100vh;
  background-color: #fff;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .wrapper {
    padding: 15px 15px;
    .card-item {
      background: #ffffff;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      padding: 10px 10px;
      margin-bottom: 10px;
      .titile {
        display: flex;
        align-items: center;
        img {
          height: 17px;
          margin-right: 12px;
        }
        p {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
      }
      .content {
        display: flex;
        flex-wrap: wrap;
        .van-button {
          margin: 5px 5px;
        }
      }
    }
    .none-border {
      box-shadow: none;
      border: none;
    }
  }
}
// 重定义p标签样式
p {
  margin: 0;
}
// 按钮活跃颜色
.active {
  background-color: rgb(255, 127, 76);
  border: none;
  color: #fff;
}
.none-active {
  background-color: #fff;
  border-color: 1px solid #ebedf0;
  color: #333;
}
// 单元格样式
.cell-style {
  color: #969799;
}
h1 {
  color: #111;
  font-size: 14px;
  margin-left: 20px;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--primary-color);
    line-height: 14px;
    vertical-align: middle;
    margin-right: 5px;
  }
}
.product-choose {
  .item {
    display: flex;
    margin: 15px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #ededed;
    .img-box {
      width: 67px;
      height: 78px;
      line-height: 100px;
      background-color: #eee;
      margin-right: 10px;
      img {
        width: 100%;
      }
    }
    .content-box {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;
      div:first-child {
        color: #111;
        font-size: 16px;
        font-weight: bold;
      }
      div:nth-child(2) {
        color: #999;
        font-size: 13px;
      }
      div:last-child {
        display: flex;
        justify-content: space-between;
        span:first-child {
          color: #ff3c3c;
          font-size: 23px;
        }
        span:last-child {
          display: inline-block;
          width: 79px;
          height: 27px;
          line-height: 27px;
          border-radius: 4px;
          text-align: center;
          color: #fff;
          background-color: var(--primary-color);
        }
      }
    }
  }
}
</style>
