<template>
  <div class="upload">
    <div class="wrapper">
      <div class="top">
        <img src="@/assets/imgs/manage/payMoney.png" alt="" />
        <span>应收保费</span>
        <span>￥{{ product.reference_price }}</span>
      </div>
      <div class="middle">
        <van-uploader v-model="fileList" :max-count="1" :max-size="4 * 1024 * 1024">
          <div class="tips"><span>点击上传凭证</span><span>（支持jpg、png、pdf，文件大小不超过 4M）</span></div>
        </van-uploader>
      </div>
      <!-- <div class="tips">（支持jpg、png、pdf，文件大小不超过2M）</div> -->
      <div class="btn">
        <van-button type="primary" block :color="primaryColor" @click="uploadFile">上传凭证</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { uploadFile, productDetail } from '@/apis/policy'

export default {
  data() {
    return {
      fileList: [],
      product: {}
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  created() {
    productDetail(this.$route.query.product_id).then((r) => {
      this.product = r.data.data
    })
  },
  methods: {
    uploadFile() {
      const _data = Object.assign({}, { proof: this.fileList[0].file })
      const _id = this.$route.params.id
      uploadFile(_id, _data).then((r) => {
        this.$toast.success('上传成功')

        this.$router.push({ name: 'Policies' })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.upload {
  min-height: 100vh;
  background-color: #ededed;
  .wrapper {
    background-color: #fff;
    display: flex;
    align-items: center;
    flex-direction: column;
    .top {
      display: flex;
      align-items: center;
      margin-top: 36px;
      img {
        width: 24px;
      }
      span {
        font-size: 16px;
        color: #333;
        font-weight: 18px;
      }
      span:last-child {
        color: #ea062b;
      }
    }
    .middle {
      margin-top: 16px;
    }
    .tips {
      color: var(--primary-color);
      font-size: 9px;
      margin-top: 12px;
      border: 1px solid #ededed;
      border-radius: 6px;
      padding: 2px 4px;
      display: flex;
      align-items: center;
      flex-direction: column;
    }
    .btn {
      margin-top: 36px;
      width: 100%;
      box-sizing: border-box;
    }
  }
}
</style>
