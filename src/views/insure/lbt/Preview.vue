<template>
  <div class="pay-computed">
    <div class="box-one">
      <div class="flex-box">
        <div class="left-box">
          <div class="img-box">
            <img src="@/assets/imgs/insure/pay.png" alt="" />
          </div>
          <div class="left-word-box">
            <span>运费金额 ￥{{ data.coverage }}</span>
            <span>费率 {{ data.user_rate }}‱</span>
          </div>
        </div>
        <div class="right-box">
          <span>保费 ￥{{ data?.user_premium?.toFixed(2) }}</span>
        </div>
      </div>
      <van-divider />
      <ul class="box-one-bottom">
        <li class="item">
          <span>保险公司</span><span>{{ product.company_branch }}</span>
        </li>
        <li class="item">
          <span>主条款</span>
          <span>{{ data.clauses.main }} </span>
        </li>
        <li class="long-text">
          <p>免赔</p>
          <p>{{ product.additional.deductible }}</p>
        </li>
        <li class="long-text">
          <p>特别约定</p>
          <p>{{ product.additional.special_agreement }}</p>
        </li>
      </ul>
    </div>
    <div class="des-detail">投保详情</div>
    <van-cell-group>
      <van-cell>
        <template #title>
          <div class="title-box">
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span>基本信息</span>
          </div>
        </template>
      </van-cell>
      <van-cell title="投保人" :value="data.policyholder" />
      <van-cell title="被保人" :value="data.insured" />
      <van-cell title="投保人地址" :value="data.policyholder_address" />
      <van-cell title="被保人地址" :value="data.insured_address" />
      <van-cell title="投保人电话" :value="data.policyholder_phone_number" />
      <van-cell title="被保人电话" :value="data.insured_phone_number" />
    </van-cell-group>
    <van-cell-group>
      <van-cell>
        <template #title>
          <div class="title-box">
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span>货物信息</span>
          </div>
        </template>
      </van-cell>
      <van-cell title="货物类别" :value="data.goods_type.name" />
      <van-cell title="装载方式" :value="data.loading_method.name" />
      <van-cell title="运输方式" :value="data.transport_method ? data.transport_method?.name : ''" />
      <van-cell title="包装" :value="data.packing_method ? data.packing_method?.name : ''" />
      <van-cell title="货物名称" :value="data.goods_name" />
      <van-cell title="货物数量" :value="data.goods_amount" />
    </van-cell-group>
    <van-cell-group>
      <van-cell>
        <template #title>
          <div class="title-box">
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span>运输信息</span>
          </div>
        </template>
      </van-cell>
      <van-cell title="发票号" :value="data.invoice_no" />
      <van-cell title="运单号" :value="data.waybill_no" />
      <van-cell title="车牌号" :value="data.transport_no" />
      <van-cell title="起运日期" :value="data.shipping_date" />
      <van-cell title="起运地" :value="data.departure" />
      <van-cell title="中转地" :value="data.transmit" />
      <van-cell title="目的地" :value="data.destination" />
    </van-cell-group>
    <div class="btn-box">
      <div @click="comeBack">返回修改</div>
      <div @click="handleSubmit">确认投保</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    product: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    handleSubmit() {
      this.$emit('on-submit', Object.assign({}, this.data))
    },
    comeBack() {
      this.$emit('on-come-back')
    }
  }
}
</script>

<style lang="less" scoped>
.pay-computed {
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .box-one {
    padding: 15px;
    margin-top: 8px;
    background-color: #fff;
    .flex-box {
      display: flex;
      justify-content: space-between;
      .left-box {
        display: flex;
        justify-content: flex-start;
        .img-box {
          width: 42px;
          overflow: hidden;
          img {
            width: 100%;
            background-size: contain;
          }
        }
        .left-word-box {
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          margin-left: 10px;
          span {
            color: #999;
            font-size: 15px;
            font-weight: bold;
          }
        }
      }
      .right-box {
        line-height: 40px;
        span {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
      }
    }
    .box-one-bottom {
      .item {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        span:first-child {
          color: #999;
          font-size: 14px;
        }
        span:last-child {
          color: #333;
          font-size: 14px;
        }
      }
      .long-text {
        justify-content: space-between;
        margin-top: 15px;
        p:first-child {
          color: #999;
          font-size: 14px;
        }
        p:last-child {
          color: #333;
          font-size: 14px;
        }
      }
    }
  }
  .des-detail {
    color: #666;
    font-size: 16px;
    text-align: center;
    margin: 8px 0;
  }
  .check-box {
    padding: 6px 15px;
    .word {
      color: #333;
      font-size: 14px;
      i {
        font-style: normal;
        color: var(--primary-color);
      }
    }
  }
  .btn-box {
    margin-top: 6px;
    display: flex;
    div {
      height: 40px;
      font-size: 14px;
      line-height: 40px;
    }
    div:first-child {
      flex: 1;
      color: #333;
      background-color: #fff;
      text-align: center;
    }
    div:last-child {
      text-align: center;
      flex: 1;
      color: #fff;
      background-color: var(--primary-color);
    }
  }
}
.title-box {
  display: flex;
  align-items: center;
  img {
    width: 14px;
    margin-right: 8px;
  }
  span {
    color: #333;
    font-size: 16px;
    font-weight: bold;
  }
}
.van-popup {
  padding: 0 15px;
  box-sizing: border-box;
  p:not(p:first-child) {
    color: #333;
    font-size: 13px;
    text-indent: 26px;
    margin: 0;
    word-wrap: break-word;
  }
  p:first-child {
    text-align: center;
    color: #000;
    font-size: 17px;
    font-weight: bold;
  }
  .van-button {
    margin: 8px 0;
  }
}
</style>
