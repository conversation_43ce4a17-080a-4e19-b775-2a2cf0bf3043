<template>
  <div class="insure-box">
    <div class="square-box">
      <div class="item" @click="$router.push({ name: 'InsureDomestic' })" v-if="features?.includes('insure.domestic')">
        <h3 class="heading">国内货运险</h3>
        <p class="slogan">
          出单时效快 <br />
          普通责任免赔可加费调整
        </p>
        <span class="link">
          <img src="@/assets/imgs/insure/go.png" alt="" />
        </span>
        <img src="@/assets/imgs/insure/domestic.png" alt="Domestic" />
      </div>
      <div class="item" @click="$router.push({ name: 'InsureInternational' })" v-if="features?.includes('insure.intl')">
        <h3 class="heading">国际货运险</h3>
        <p class="slogan">
          国内外海陆空均有保障 <br />
          一纸的托付，全程的服务
        </p>
        <span class="link">
          <img src="@/assets/imgs/insure/go.png" alt="" />
        </span>
        <img src="@/assets/imgs/insure/intl.png" alt="Intl" />
      </div>
    </div>
    <div class="square-box" :style="{ marginTop: '4%' }" v-if="features?.includes('insure.group')">
      <div class="item" @click="$router.push({ name: 'InsureGroup' })">
        <h3 class="heading">雇主责任险</h3>
        <p class="slogan">
          转嫁雇主的法律责任 <br />
          免除雇主后顾之忧
        </p>
        <span class="link">
          <img src="@/assets/imgs/insure/go.png" alt="" />
        </span>
        <img src="@/assets/imgs/insure/group.png" alt="Group" />
      </div>
      <div class="item" @click="$router.push({ name: 'InsureLbt' })" v-if="features?.includes('insure.lbt')">
        <h3 class="heading">单车责任险</h3>
        <p class="slogan">
          手续简单，出单快 <br />
          选择尽放心
        </p>
        <span class="link">
          <img src="@/assets/imgs/insure/go.png" alt="" />
        </span>
        <img src="@/assets/imgs/insure/lbt.png" alt="Lbt" />
      </div>
    </div>
    <div
      class="full-box"
      :style="{ marginTop: '4%' }"
      @click="$router.push({ name: 'InsureOther' })"
      v-if="features?.includes('insure.general')"
    >
      <div class="item item-img">
        <img src="@/assets/imgs/insure/other.png" alt="Other" />
      </div>
      <div class="item item-text">
        <h3 class="heading">其他险种</h3>
        <p class="slogan">
          叉车意外险、驾乘意外险 <br />
          更多险种等你选择
        </p>
        <a class="show-more"> 查看更多 </a>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Insure',
  computed: {
    ...mapGetters(['features'])
  }
}
</script>

<style lang="less" scope>
.insure-box {
  height: 100%;
  width: 100vw;
  box-sizing: border-box;
  padding: 15px;

  .square-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .item {
      h3,
      p,
      span,
      img {
        padding: 0;
        margin: 0;
      }

      flex-basis: 48%;
      background: #f5f5f5;
      text-align: center;
      box-sizing: border-box;
      padding: 11px;
      border-radius: 4px;

      .heading {
        font-size: 16px;
        color: #333333;
      }

      .link {
        display: inline-block;
        text-align: center;
        padding: 10px 0 12px 0;

        img {
          display: inline-block;
          width: 18px;
        }
      }

      .slogan {
        margin-top: 9px;
        font-size: 13px;
        color: #999999;
      }

      img {
        display: inline-block;
        box-sizing: border-box;
        padding: 0 3px 0 3px;
        width: 100%;
      }
    }
  }

  .full-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f5f5f5;
    box-sizing: border-box;

    .item {
      flex: 1;
      flex-basis: 48%;
      border-radius: 4px;
    }

    .item-img {
      box-sizing: border-box;
      padding: 10px 22px 10px 22px;
      img {
        width: 100%;
      }
    }

    .item-text {
      box-sizing: border-box;
      padding: 10px 14px 10px 14px;
      text-align: center;

      h3,
      p,
      span,
      img {
        padding: 0;
        margin: 0;
      }

      .heading {
        font-size: 16px;
        color: #333333;
      }

      .slogan {
        margin-top: 9px;
        font-size: 13px;
        color: #999;
      }

      .show-more {
        display: inline-flex;
        margin-top: 8px;
        background-color: var(--primary-color);
        padding: 3px 10px 3px 10px;
        border-radius: 4px;
        font-size: 13px;
        color: #ffffff;
      }
    }
  }
}
</style>
