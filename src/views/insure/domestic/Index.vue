<template>
  <div class="gzhyx">
    <van-sticky>
      <van-nav-bar title="国内货运险" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <steps :active="currentStep" />

    <template v-if="$route.query.from === 'copy' && data.order_no !== undefined">
      <van-notice-bar :text="`您正在复制${data.order_no}投保`" left-icon="volume-o" style="margin-top: 10px" />
    </template>

    <div>
      <by-form
        v-show="currentStep === 0"
        :data="Object.assign({}, data)"
        @on-preview="handlePreview"
        @on-draft="handleDraft"
      />
      <by-preview
        v-if="currentStep === 1"
        :data.sync="data"
        :product.sync="product"
        @on-submit="handleSubmit"
        @on-come-back="handleComeBack"
      />
    </div>
  </div>
</template>

<script>
import { preview, insure, getPolicy, updatePolicy, staging } from '@/apis/policy'
import Steps from './../components/Steps'
import Form from './Form.vue'
import Preview from './Preview.vue'
import { findAreaCode } from '@/utils/func'

const PRODUCT_TYPE = 1

export default {
  name: 'InsureDomestic',
  components: {
    Steps,
    'by-form': Form,
    'by-preview': Preview
  },
  data() {
    return {
      currentStep: 0,
      data: {},
      product: {}
    }
  },
  created() {
    if (this.$route.query.id !== undefined) {
      getPolicy(this.$route.query.id).then((r) => {
        const data = r.data.data
        const [departure, departureAddr] = data.detail?.departure?.split(':') || []
        const [destination, destinationAddr] = data.detail?.destination?.split(':') || []
        const [transmit, transmitAddr] = data.detail?.transmit?.split(':') || []

        let policy = {
          type: PRODUCT_TYPE,
          is_new: data.detail?.is_new,
          order_no: data.order_no,
          product_id: data.product?.id,
          subject_id: data?.detail?.subject?.id,
          manual_conditions: data?.detail?.manual_conditions,
          subject_category_ids: data.detail?.subject_categories?.map((e) => e.id) || [],
          policyholder: data.policyholder,
          policyholder_type: data.policyholder_type,
          policyholder_overseas: data.policyholder_overseas,
          policyholder_idcard_no: data.policyholder_idcard_no,
          policyholder_idcard_issue_date: data.policyholder_idcard_issue_date,
          policyholder_idcard_valid_till: data.policyholder_idcard_valid_till,
          insured: data.insured,
          insured_type: data.insured_type,
          insured_idcard_no: data.insured_idcard_no,
          insured_overseas: data.insured_overseas,
          insured_idcard_issue_date: data.insured_idcard_issue_date,
          insured_idcard_valid_till: data.insured_idcard_valid_till,
          policyholder_phone_number: data.policyholder_phone_number,
          insured_phone_number: data.insured_phone_number,
          policyholder_address: data.policyholder_address,
          insured_address: data.insured_address,
          sticky_note: data.striky_note,
          goods_type_id: data.detail?.goods_type?.id,
          loading_method_id: data.detail?.loading_method?.id,
          packing_method_id: data.detail?.packing_method?.id,
          goods_name: data.detail?.goods_name,
          goods_amount: data.detail?.goods_amount,
          ship_construction_year: data.detail?.ship_construction_year,
          transport_method_id: data.detail?.transport_method?.id,
          departure: departure,
          departure_port: data.detail?.departure_port,
          departure_code: findAreaCode(departure),
          departure_addr: departureAddr || '',
          destination: destination,
          destination_port: data.detail?.destination_port,
          destination_code: findAreaCode(destination),
          destination_addr: destinationAddr || ''
        }

        // 编辑需要填充所有的
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          policy = Object.assign(policy, {
            id: data.id,
            coverage: data.coverage,
            transport_no: data.detail?.transport_no,
            invoice_no: data.detail?.invoice_no,
            waybill_no: data.detail?.waybill_no,
            shipping_date: Date.parse(data.detail?.shipping_date),
            transmit: transmit || '',
            transmit_port: data.detail?.transmit_port,
            transmit_code: findAreaCode(transmit),
            transmit_addr: transmitAddr || '',
            remark: data.remark
          })
        }

        const ticket = data.tickets.find((e) => e.status === 4)?.revision || {}
        if (ticket?.shipping_date !== undefined) {
          ticket.shipping_date = Date.parse(ticket.shipping_date)
        }

        policy = Object.assign({}, policy, ticket)

        this.data = policy
      })
    }
  },
  methods: {
    handlePreview(data, product) {
      if (data.id !== undefined && this.$route.query.from === 'edit') {
        const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
        if (this.$route.query.ticket_id !== undefined) {
          data.ticket_id = this.$route.query.ticket_id
        }
        updatePolicy(data.id, data).then((r) => {
          loading.clear()
          this.$toast.success('提交成功')
          if (this.data.payment_method === 2 && r?.payment_url) {
            window.open(r.payment_url, '_blank')
          }

          this.$router.push({ name: 'Policies', query: { active: 1 } })
        })
      } else {
        const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
        preview(data).then((r) => {
          loading.clear()
          if (data.id !== undefined) {
            this.data = Object.assign({}, r.data.data, { id: data.id })
            this.data.policy_id = data.id
          } else {
            this.data = Object.assign({}, r.data.data, data)
          }

          this.data.anti_dated_file = data.anti_dated_file
          this.data.custom_file = data.custom_file

          this.currentStep++
        })

        this.product = product
      }
    },
    handleSubmit(data) {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      if (this.$route.query.from === 'copy') {
        delete data.id
        delete data.policy_id
      }
      insure(data).then((r) => {
        this.$toast.success('已提交')
        if (data.payment_method == 2 && r.data.data.payment_url) {
          window.location.href = r.data.data.payment_url
          return
        } else {
          this.$router.push({ name: 'Policies', query: { active: 1 } })
        }
        loading.clear()
      })
    },
    handleComeBack() {
      this.currentStep--
    },
    handleDraft(data, product) {
      if (this.$route.params.id) {
        if (['edit', 'continue'].includes(this.$route.query.from)) {
          data.policy_id = this.$route.params.id
        }
      }

      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      staging(data).then(() => {
        loading.clear()
        this.$toast.success('保存成功')

        this.$router.push({ name: 'Policies', query: { active: 1 } })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.gzhyx {
  background: #ededed;
}
</style>
