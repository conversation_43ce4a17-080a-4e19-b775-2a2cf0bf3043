<template>
  <div class="employerPage">
    <van-sticky>
      <van-nav-bar title="产品选择" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <div class="wrapper">
      <div class="card-item">
        <div class="titile">
          <img src="@/assets/imgs/insure/choose_2.png" alt="" />
          <p>保险公司</p>
        </div>
        <van-divider />
        <div class="content" id="content" @click="chooseCompany">
          <van-button v-for="item in allData" :key="item.id" :data-company="item.id" size="small" round>{{
            item.name
          }}</van-button>
        </div>
      </div>
      <div class="card-item">
        <div class="titile">
          <img src="@/assets/imgs/insure/choose_1.png" alt="" />
          <p>保险产品</p>
        </div>
        <van-divider />
        <template v-if="this.chooseoptions.company">
          <div class="content" id="product" @click="chooseProduct">
            <van-button v-for="item in productList" :key="item.id" :data-company="item.id" size="small" round>
              {{ item.name }}
            </van-button>
          </div>
        </template>
        <template v-else>
          <van-notice-bar text="请先选择保险公司" />
        </template>
      </div>
      <div class="card-item">
        <div class="titile">
          <img src="@/assets/imgs/insure/choose_4.png" alt="" />
          <p>产品套餐</p>
        </div>
        <van-divider />
        <template v-if="this.chooseoptions.product">
          <div class="content" id="package" @click="choosePackage">
            <van-button v-for="item in plansList" :key="item.id" :data-company="item.id" size="small" round>
              {{ item.title }}
            </van-button>
          </div>
        </template>
        <template v-else>
          <van-notice-bar text="请先选择保险产品" />
        </template>
      </div>
      <div class="card-item" v-if="this.chooseoptions.package">
        <div class="titile">
          <img src="@/assets/imgs/insure/choose_3.png" alt="" />
          <p>套餐内容</p>
        </div>
        <van-cell :value="tcData.title">
          <template #title>
            <span class="cell-style">类目名称</span>
          </template>
        </van-cell>
        <van-cell>
          <template #title>
            <span class="cell-style">意外身故/残疾</span>
          </template>
          <template #label>
            <div class="html-body" v-html="tcData.accidental_death"></div>
          </template>
        </van-cell>
        <van-cell>
          <template #title>
            <span class="cell-style">意外医疗</span>
          </template>
          <template #label>
            <div class="html-body" v-html="tcData.accidental_medical"></div>
          </template>
        </van-cell>
        <van-cell v-if="tcData.accidental_injury">
          <template #title>
            <span class="cell-style">附加24小时意外伤害</span>
          </template>
          <template #label>
            <div class="html-body" v-html="tcData.accidental_injury"></div>
          </template>
        </van-cell>
        <van-cell v-if="tcData.lost_wages" :value="tcData.lost_wages">
          <template #title>
            <span class="cell-style">误工费</span>
          </template>
        </van-cell>
        <van-cell :value="tcData.accidental_allowance">
          <template #title>
            <span class="cell-style">意外伤害住院津贴</span>
          </template>
        </van-cell>
        <van-cell :value="tcData.legal_liability">
          <template #title>
            <span class="cell-style">雇主法律责任</span>
          </template>
        </van-cell>
        <van-cell :value="tcData.total_indemnity">
          <template #title>
            <span class="cell-style">累计赔偿限额</span>
          </template>
        </van-cell>
      </div>
      <div class="card-item" v-if="this.chooseoptions.package">
        <div class="titile">
          <img src="@/assets/imgs/insure/money_icon.png" alt="" />
          <p>价格信息</p>
        </div>
        <van-cell v-for="job in jobs" :key="job.id" :value="'¥' + job.price">
          <template #title>
            <span class="cell-style">{{ job.name }}</span>
          </template>
        </van-cell>
        <van-cell v-if="tcData.jobs.length > 10 && !showFullJobs">
          <a @click="showFullJobs = true">查看全部</a>
        </van-cell>
      </div>
      <div class="card-item" v-if="chooseoptions.product">
        <div class="titile">
          <img src="@/assets/imgs/insure/yd.png" alt="" />
          <p>产品简介</p>
        </div>
        <van-divider />
        <van-cell>
          <div v-html="product.additional.description || '-'"></div>
        </van-cell>
      </div>
      <div class="card-item" v-if="chooseoptions.product">
        <div class="titile">
          <img src="@/assets/imgs/insure/yd.png" alt="" />
          <p>产品备注</p>
        </div>
        <van-divider />
        <van-cell>
          <div v-html="product.additional.remark || '-'"></div>
        </van-cell>
      </div>
      <div class="card-item" v-if="product?.additional?.payment_type === 2">
        <div class="titile">
          <img src="@/assets/imgs/insure/yd.png" alt="" />
          <p>支付提醒</p>
        </div>
        <van-divider />
        <van-notice-bar text="该产品为见费出单需等待审核完成后完成支付方可生效" />
      </div>
      <div class="card-item" v-if="chooseoptions.product">
        <div class="titile">
          <img src="@/assets/imgs/insure/xz.png" alt="" />
          <p>投保须知</p>
        </div>
        <van-divider />
        <van-cell>
          <p>
            投保前请您仔细阅读：
            <span @click="showNotice = true" class="word_color"> 《投保须知及除外事项的内容》、《保险条款》 </span>
          </p>
        </van-cell>
        <van-popup
          v-model="showNotice"
          position="bottom"
          :style="{ height: '50%', padding: '10px', BoxSizing: 'border-box' }"
          round
          closeable
        >
          <h3>投保须知</h3>
          <div v-html="product.additional.notice"></div>
        </van-popup>
      </div>
      <van-button type="primary" block :color="primaryColor" style="margin: 25px 0" @click="InsuredImmediately">
        立即投保
      </van-button>
    </div>
  </div>
</template>

<script>
import { getProductList } from '@/apis/policy'
import { Dialog } from 'vant'
import { userKey } from '@/config'

export default {
  name: 'employerPage',
  data() {
    return {
      showNotice: false,
      company_id: void 0,
      product_id: void 0,
      group_plan_id: void 0,
      chooseoptions: {
        company: null,
        product: null,
        package: null
      },
      product: null,
      productList: [],
      plansList: [],
      conten: {},
      mainData: [],

      // 所有数据
      allData: [],
      showFullJobs: false,
      tcData: {
        accidental_death: '',
        accidental_medical: '',
        lost_wages: '',
        accidental_allowance: '',
        legal_liability: '',
        total_indemnity: '',
        title: ''
      }
    }
  },
  computed: {
    user() {
      return JSON.parse(localStorage.getItem(userKey))
    },
    jobs() {
      if (this.tcData.jobs.length > 10 && !this.showFullJobs) {
        return this.tcData.jobs.slice(0, 10)
      }

      return this.tcData.jobs
    }
  },
  watch: {
    company_id(newN) {
      const chooseOption = this.allData.find((item) => item.id == newN).products
      this.productList = chooseOption
    },
    product_id(newN) {
      this.product = this.productList.find((item) => item.id == newN)
      const content = this.product.plans
      this.plansList = content
    },
    group_plan_id(newN) {
      const content = this.plansList.find((item) => item.id == newN)
      this.tcData = content
    }
  },
  mounted() {
    // 获取雇主产品
    getProductList().then((r) => {
      this.allData = r.data.data
    })
  },
  methods: {
    // 选择的公司
    chooseCompany(e) {
      const items = Array.from(document.getElementById('content').getElementsByClassName('van-button'))
      items.forEach((item) => {
        item.setAttribute('class', 'van-button van-button--default van-button--small van-button--round none-active')
        if (e.target === item) {
          this.company_id = e.target.dataset.company
          e.target.setAttribute('class', 'van-button van-button--default van-button--small van-button--round active')
          this.chooseoptions.company = e.target.dataset.company
        }
      })
      this.chooseoptions.package = null
      this.chooseoptions.product = null
    },
    chooseProduct(e) {
      const items = Array.from(document.getElementById('product').getElementsByClassName('van-button'))
      items.forEach((item) => {
        item.setAttribute('class', 'van-button van-button--default van-button--small van-button--round none-active')
        if (e.target === item) {
          this.product_id = e.target.dataset.company
          e.target.setAttribute('class', 'van-button van-button--default van-button--small van-button--round active')
          this.chooseoptions.product = e.target.dataset.company
        }
      })
      this.chooseoptions.package = null
    },

    choosePackage(e) {
      const items = Array.from(document.getElementById('package').getElementsByClassName('van-button'))
      items.forEach((item) => {
        item.setAttribute('class', 'van-button van-button--default van-button--small van-button--round none-active')
        if (e.target === item) {
          this.group_plan_id = e.target.dataset.company
          e.target.setAttribute('class', 'van-button van-button--default van-button--small van-button--round active')
          this.chooseoptions.package = e.target.dataset.company
        }
      })
    },
    // 立即投保
    InsuredImmediately() {
      if (!this.company_id) {
        this.$toast('请选择保险公司')
        return
      }
      if (!this.product_id) {
        this.$toast('请选择保险产品')
        return
      }
      if (!this.group_plan_id) {
        this.$toast('请选择产品套餐')
        return
      }

      window.sessionStorage.setItem(
        'gz-write',
        JSON.stringify({
          product_id: this.product_id,
          group_plan_id: this.group_plan_id
        })
      )

      // this.$router.push(`/InsuredImmediately/${}`)
      const product = this.product
      if (product?.additional?.third_platform === 'API_GROUP_ZY') {
        Dialog.confirm({
          title: '投保声明',
          message: `1、兹双方同意，本保单承保的被保险人员工年龄范围为 16-65 周岁。
2、被保险人的雇员发生工伤事故后，有工伤保险的应当先行向工伤保险提出索赔请求。
3、本保单记名承保，投保时需同时提供雇员清单。雇员必须与被保险人存在劳动关系（包括事实劳动关系）并签署劳动合同。本保单不承保仅与被保险人存在劳务派遣关系，而不产生直接劳动关系的雇员。
4、除紧急抢救外，被保险雇员因遭受保险事故，在二级及以上公立医院治疗，但不包含任何北京平谷区密云、河北省三河市、天津滨海区、天津静海区、辽宁铁岭、河北青龙县、廊坊市、山东禹城、河南信阳下辖的各医疗机构，不在保险公司认可医院范围内，即上述地区医疗机构的发票保险公司不予理赔。对于被保险人在每次意外伤害中所支出的必要且合理的，符合当地政府颁布的基本医疗保险报销范围的医疗费用，按照实际支出的符合当地社会医疗保险规定及用药名录的合理医疗费用扣除免赔额后进行赔付。
5、被保险人应在发生事故的 48 小时之内拨打中意财产保险有限公司24小时客服热线4006002700进行报案，如超过上述报案时效保险人有权不承担相应的赔偿。
6、伤残5%起：一级伤残 100%、二级伤残 80%、三级伤残 70%、四级伤残 60%、五级伤残 50%、六级伤残 40%、七级伤残 30%、八级伤残 20%、九级伤残 10%、十级伤残 5%； 伤残10%起：一级 100%、二级 90%、三级 80%、四级 70%、五级 60%、六级 50%、七级 40%、八级 30%、九级 20%、十级 10%。
7、本保单记名投保，投保人需提供被保险人的姓名及身份证号信息。雇员必须与被保险人存在劳动关系（包括事实劳动关系）并签署劳动合同。本保单不承保仅与被保险人存在劳务派遣关系，而不产生直接劳动关系的雇员。
8、本保单仅承担中国国籍人员（不包含港澳台人员）。
9、本保险仅承保普通货物运输企业，本保险不承保营业执照经营范围无货物运输的企业、人力资源公司、劳务派遣公司、劳务外包公司、及个体经营户（统一社会信用代码92开头）。
10、本保险不承保从事以下职业的人员：运输危险品车辆的司机及装卸工、运输建筑工程相关车辆的司机及装卸工、运输钢材建材相关车辆的司机及装卸工、自卸车辆司机及装卸工、超长超宽大件运输车辆司机及装卸工、特种车辆司机及装卸工（不含集装箱货物运输车辆和冷藏货物厢式货车）、渣土混凝土车辆司机及装卸工、吊机车辆司机及装卸工、重型罐式半挂车司机及装卸工、脚离地距离5米以上高处工作人员、外卖配送人员、送餐员、搬家服务工作人员、道路救援及清障服务工作人员、钢结构工人、路桥建设工人、室外土建人员、渔船及船厂工作人员，码头工作人员、有色金属冶炼及加工业工作人员，化工行业工作人员，矿业开采与加工工作人员、潜水作业人员、玻璃幕墙（外窗）安装与清洁人员、爆破工作人员、采石（砂）凿岩工作人员。
11、本保险不承保名称中包含以下文字的被保险人:人力资源、劳务、外包、快递、速运、建筑、工程、工程机械、起重、混凝土、垃圾、建材、钢材、石材、砂石、化工、化学、救援、清障、搬家、渣土、吊机、土石方、石化。
12、伤残鉴定：保险公司接受市级以上司法鉴定机构提供的伤残鉴定。可以在以下网站查询全国各地的司法鉴定机构名录。http://www.sfjdml.com/web/。`,
          messageAlign: 'left',
          allowHtml: true,
          theme: 'round-button'
        }).then(() => {
          if (product?.channel?.platform?.includes(this.user.platform_id) && product?.additional?.inform?.length > 1) {
            Dialog.alert({
              title: '投保告知',
              message: this?.product?.additional?.inform,
              messageAlign: 'left',
              allowHtml: true,
              theme: 'round-button'
            }).then(() => {
              this.$router.push({
                name: 'InsureGroupZhongyi',
                query: { product_id: product.id }
              })
            })
          } else {
            this.$router.push({
              name: 'InsureGroupZhongyi',
              query: { product_id: product.id }
            })
          }
        })
      } else {
        Dialog.confirm({
          title: '投保声明',
          message: ` 1. 投保人确认投保前已认真阅读并了解了本声明以及投保须知的内容，并如实按照投保须知的指引进行投保。
2.本公司及保险公司已向投保人提供并详细介绍了保单所适用的所有保险条款，并对其中免除保险人责任的条款（包括但不限于责任免除、投保人、被保险人义务、赔偿处理、其他事项等），以及保单付费约定和特别约定的内容向投保人做了明确说明，投保人已充分理解并接受上述内容，同意以此作为投保的依据，自愿投保本保险。
3.如投保人违反本声明，未做到如实投保，须自行承担由此引发的任何责任和后果。对于本声明有任何异议，请在确认投保前与本公司联系。`,
          messageAlign: 'left',
          allowHtml: true,
          theme: 'round-button'
        }).then(() => {
          if (product?.channel?.platform?.includes(this.user.platform_id) && product?.additional?.inform?.length > 1) {
            Dialog.alert({
              title: '投保告知',
              message: this?.product?.additional?.inform,
              messageAlign: 'left',
              allowHtml: true,
              theme: 'round-button'
            }).then(() => {
              this.$router.push({
                name: 'InsureGroupBasic',
                query: { product_id: product.id }
              })
            })
          } else {
            this.$router.push({
              name: 'InsureGroupBasic',
              query: { product_id: product.id }
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.word_color {
  color: var(--primary-color);
}

.html-body {
  /deep/ tr,
  /deep/ th,
  /deep/ td {
    height: auto;
    border: 1px solid #eee !important;
    border-spacing: 0;
    padding: 0;
    min-height: 30px;
    padding: 5px;
  }
}

.employerPage {
  min-height: 100vh;
  background-color: #fff;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .wrapper {
    padding: 15px 15px;
    .card-item {
      background: #ffffff;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      padding: 10px 10px;
      margin-bottom: 10px;
      .titile {
        display: flex;
        align-items: center;
        img {
          height: 17px;
          margin-right: 12px;
        }
        p {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
      }
      .content {
        display: flex;
        flex-wrap: wrap;
        .van-button {
          margin: 5px 5px;
        }
      }
    }
    .none-border {
      box-shadow: none;
      border: none;
    }
  }
}
// 重定义p标签样式
p {
  margin: 0;
}
// 按钮活跃颜色
.active {
  background-color: rgb(255, 127, 76);
  border: none;
  color: #fff;
}
.none-active {
  background-color: #fff;
  border-color: 1px solid #ebedf0;
  color: #333;
}
// 单元格样式
.cell-style {
  color: #969799;
}
</style>
