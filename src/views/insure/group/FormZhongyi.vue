<template>
  <div class="insured-immediately">
    <van-sticky>
      <van-nav-bar title="保单填写" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-form @submit="onSubmit">
      <!-- 投保人信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">投保人信息</span>
          </template>
        </van-cell>
        <van-field
          required
          v-model="form.policyholder"
          name="企业法人"
          placeholder="请输入企业法人"
          :rules="[{ required: true, message: '请输入企业法人' }]"
        >
          <template #label>
            <span>企业法人</span>
          </template>
        </van-field>
        <van-field
          required
          v-model="form.policyholder_phone_number"
          name="手机"
          placeholder="请输入手机"
          :rules="[{ required: true, message: '请输入手机' }]"
        >
          <template #label>
            <span>手机</span>
          </template>
        </van-field>
        <van-field
          readonly
          clickable
          required
          name="policyholder_idcard_type"
          :value="form.policyholder_idcard_type"
          label="证件类型"
          placeholder="请选择证件类型"
          :rules="[{ required: true, message: '请选择证件类型' }]"
          @click="showCertTypePicker = true"
        />
        <van-popup v-model="showCertTypePicker" position="bottom">
          <van-picker
            show-toolbar
            :columns="certTypes"
            @confirm="onCertTypePicked"
            @cancel="showCertTypePicker = false"
          />
        </van-popup>
        <van-field
          required
          v-model="form.policyholder_idcard_no"
          name="证件号"
          placeholder="请输入证件号"
          :rules="[{ required: true, message: '请输入证件号' }]"
        >
          <template #label>
            <span>证件号</span>
          </template>
        </van-field>
      </div>
      <!-- 被保人信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">被保人信息</span>
          </template>
        </van-cell>

        <van-field
          required
          v-model="form.insured"
          name="企业名称"
          placeholder="请输入企业名称"
          :rules="[{ required: true, message: '请输入企业名称' }]"
        >
          <template #label>
            <span class="required">企业名称</span>
          </template>
        </van-field>
        <van-field
          required
          v-model="form.insured_phone_number"
          name="手机"
          placeholder="请输入手机"
          :rules="[{ required: true, message: '请输入手机' }]"
        >
          <template #label>
            <span>手机</span>
          </template>
        </van-field>
        <van-field
          required
          clickable
          name="insured_cert_type"
          :value="form.insured_idcard_type"
          label="证件类型"
          placeholder="请选择证件类型"
          :rules="[{ required: true, message: '请选择证件类型' }]"
          @click="showInsuredCertTypePicker = true"
        />
        <van-popup v-model="showInsuredCertTypePicker" position="bottom">
          <van-picker
            show-toolbar
            :columns="insuredCertTypes"
            @confirm="onInsuredCertTypePicked"
            @cancel="showInsuredCertTypePicker = false"
          />
        </van-popup>
        <van-field
          required
          v-model="form.insured_idcard_no"
          name="证件号"
          placeholder="请输入证件号"
          :rules="[{ required: true, message: '请输入证件号' }]"
        >
          <template #label>
            <span>证件号</span>
          </template>
        </van-field>
      </div>
      <!-- 标的信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">标的信息</span>
          </template>
        </van-cell>
        <van-field
          required
          clickable
          name="object_address"
          :value="form.extra_info.object_address_string"
          label="地址"
          :columns-num="2"
          placeholder="点击选择省市"
          @click="showSubjectPicker = true"
        />
        <van-popup v-model="showSubjectPicker" position="bottom">
          <van-area :area-list="subjectList" @confirm="onSubjectPicked" @cancel="showSubjectPicker = false" />
        </van-popup>
        <van-field
          required
          v-model="form.extra_info.object_address_detail"
          name="object_address_detail"
          placeholder="请输入详细地址信息"
          :rules="[{ required: true, message: '请输入详细地址信息' }]"
        >
          <template #label>
            <span>详细地址</span>
          </template>
        </van-field>
      </div>
      <!-- 保单相关信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">保单相关信息</span>
          </template>
        </van-cell>
        <van-field
          required
          clickable
          name="datetimePicker"
          v-model="form.start_at"
          placeholder="点击选择时间"
          :rules="[{ required: true, message: '请选择起保日期' }]"
          @click="showCalendar = true"
        >
          <template #label>
            <span>起保日期</span>
          </template>
        </van-field>
        <van-calendar
          :color="primaryColor"
          title="起保日期"
          v-model="showCalendar"
          :min-date="minDate"
          @confirm="onConfirm"
        />
        <van-field
          readonly
          required
          v-model="form.end_at"
          name="终保日期"
          placeholder="请选择起保日期"
          :rules="[{ required: true, message: '请选择起保日期' }]"
        >
          <template #label>
            <span>终保日期</span>
          </template>
        </van-field>
        <van-cell>
          <template #title>
            <van-checkbox v-model="form.check" :checked-color="primaryColor">
              我已详细阅读
              <span class="word_color" @click="showNotice = true">投保须知及除外事项</span>的内容
            </van-checkbox>
          </template>
        </van-cell>
        <van-popup
          v-model="showNotice"
          position="bottom"
          :style="{ height: '50%', padding: '10px', BoxSizing: 'border-box' }"
          round
          closeable
        >
          <h3>投保须知</h3>
          <div v-html="this.product?.additional?.notice"></div>
        </van-popup>
        <div class="btn-box">
          <van-button :disabled="!form.check" block :color="primaryColor" size="small" native-type="submit">
            暂存，下一步
          </van-button>
        </div>
      </div>
    </van-form>
    <div class="item-wrapper"></div>
  </div>
</template>

<script>
import { insureZhongyiGroup } from '@/apis/policy'
import { getGroupProduct } from '@/apis/product'
import districts from '@/utils/zhongyi-areadata.json'
import dayjs from 'dayjs'
import { insuredPolicyForm } from '@/apis/policy'

export default {
  name: 'InsureGroupZhongyi',
  data() {
    return {
      showNotice: false,
      product: null,
      value: '',
      certTypes: [
        { value: '01', text: '身份证' },
        { value: '02', text: '军官证' },
        { value: '03', text: '学生证' },
        { value: '04', text: '台胞证' },
        { value: '06', text: '护照' },
        { value: '07', text: '港澳返乡证' },
        { value: '08', text: '出生证明（未成年人）' },
        { value: '09', text: '营业执照' },
        { value: '10', text: '工商登记号' },
        { value: '11', text: '组织机构代码' },
        { value: '13', text: '统一社会信用代码' },
        { value: '14', text: '港澳台居民居住证' },
        { value: '99', text: '其他' }
      ],
      insuredCertTypes: [
        { value: '01', text: '身份证' },
        { value: '02', text: '军官证' },
        { value: '03', text: '学生证' },
        { value: '04', text: '台胞证' },
        { value: '06', text: '护照' },
        { value: '07', text: '港澳返乡证' },
        { value: '08', text: '出生证明（未成年人）' },
        { value: '09', text: '营业执照' },
        { value: '10', text: '工商登记号' },
        { value: '11', text: '组织机构代码' },
        { value: '13', text: '统一社会信用代码' },
        { value: '14', text: '港澳台居民居住证' },
        { value: '99', text: '其他' }
      ],
      showCertTypePicker: false,
      showInsuredCertTypePicker: false,
      showSubjectPicker: false,
      subjectList: districts,
      showCalendar: false,
      showCalendar1: false,
      showPicker2: false,
      invoice_type: 'none',
      minDate: (() => {
        const today = new Date()
        const tomorrow = new Date(today)
        tomorrow.setDate(today.getDate() + 1)
        return tomorrow
      })(),
      form: {
        product_platform: 'API_GROUP_ZY',
        policyholder: '',
        policyholder_idcard_no: '',
        insured: '',
        insured_idcard_no: '',
        insured_phone_number: '',
        extra_info: {
          policyholder_idcard_type: '',
          insured_idcard_type: '',
          object_address_detail: '',
          object_address: [],
          object_address_string: ''
        }
      },
      is_show: false
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  created() {
    this.fetchGroupProductDetail()
  },
  methods: {
    fetchGroupProductDetail() {
      if (this.$route.query.product_id) {
        const productId = isNaN(parseInt(this.$route.query.product_id)) ? 0 : parseInt(this.$route.query.product_id)

        if (productId) {
          getGroupProduct(productId).then((r) => {
            this.product = r.data.data
          })
        }
      }

      if (this.$route.query.product_id) {
        const policyGroupId = isNaN(parseInt(this.$route.query.policy_group_id))
          ? 0
          : parseInt(this.$route.query.policy_group_id)

        if (policyGroupId) {
          insuredPolicyForm(policyGroupId).then((r) => {
            const policyForm = r.data.data.form
            this.form = Object.assign({}, this.form, policyForm)

            const policyholderIdType = policyForm.policyholder_idcard_type
            const insuredIdType = policyForm.insured_idcard_type

            for (let type of this.certTypes) {
              if (type.value == policyholderIdType) {
                this.form.extra_info.policyholder_idcard_type = type.value
                this.form.policyholder_idcard_type = type.text
              }
              if (type.value == insuredIdType) {
                this.form.extra_info.insured_idcard_type = type.value
                this.form.insured_idcard_type = type.text
              }
            }
          })
        }
      }
    },
    onCertTypePicked(option) {
      this.form.extra_info.policyholder_idcard_type = option.value
      this.form.policyholder_idcard_type = option.text
      this.showCertTypePicker = false
    },
    onInsuredCertTypePicked(option) {
      this.form.extra_info.insured_idcard_type = option.value
      this.form.insured_idcard_type = option.text
      this.showInsuredCertTypePicker = false
    },
    onSubjectPicked(option) {
      option = option.filter((e) => e)
      this.showSubjectPicker = false
      const address = option.map((m) => m.name)
      this.form.extra_info.object_address = address
      this.form.extra_info.object_address_string = address.join('/')
    },
    onClickLeft() {
      this.$router.go(-1)
    },
    // 起保时间
    onConfirm(date) {
      this.form.start_at = dayjs(date).format('YYYY-MM-DD 00:00:00')
      this.showCalendar = false

      this.form.end_at = dayjs(date).add(1, 'year').add(-1, 'day').format('YYYY-MM-DD 23:59:59')
    },
    onSubmit(values) {
      const _obj = JSON.parse(window.sessionStorage.getItem('gz-write'))
      const _temp = Object.assign({}, this.form, _obj)
      for (const k in _temp) {
        if (_temp[k] instanceof Array) {
          _temp[k] = _temp[k][0].file
        }
      }
      _temp.invoice_type = this.invoice_type

      if (
        parseInt(this.form.extra_info.insured_idcard_type, 10) === 13 &&
        String(this.form.insured_idcard_no).startsWith('92')
      ) {
        return this.$toast.fail('暂不支持个体工商户为被保险人')
      }

      const blacklistWords = [
        '人力资源',
        '劳务',
        '外包',
        '快递',
        '速运',
        '建筑',
        '工程',
        '工程机械',
        '起重',
        '混凝土',
        '垃圾',
        '建材',
        '钢材',
        '石材',
        '砂石',
        '化工',
        '化学',
        '救援',
        '清障',
        '搬家',
        '牵引车',
        '自卸车',
        '大型货物',
        '渣土',
        '吊机',
        '土石方',
        '石化'
      ]

      for (const idx in blacklistWords) {
        if (
          this.form.policyholder.indexOf(blacklistWords[idx]) !== -1 ||
          this.form.insured.indexOf(blacklistWords[idx]) !== -1
        ) {
          return this.$toast.fail('您的保单中含有黑名单关键字，请检查后重新提交')
        }
      }

      const provinceBlacklist = ['山东省', '天津市', '湖南省', '贵州省', '河北省', '辽宁省', '黑龙江省', '吉林省']
      if (
        provinceBlacklist.includes(this.form.extra_info.object_address[0]) &&
        ['装卸', '搬运'].some(
          (word) =>
            String(this.form.policyholder).indexOf(word) !== -1 || String(this.form.insured).indexOf(word) !== -1
        )
      ) {
        return this.$toast.fail('暂不符合承保规则')
      }

      insureZhongyiGroup(_temp).then((r) => {
        const _id = r.data.data.policy_group.id
        this.$router.push({ name: 'GroupPeopleList', params: { id: _id } })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.insured-immediately {
  min-height: 100vh;
  background-color: #ededed;

  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }

  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
  }
}

// 必填项
.required::after {
  content: '*';
  color: red;
}

// 保单填写
.van-cell {
  img {
    width: 12px;
    height: 15px;
    background-size: contain;
    line-height: 15px;
  }

  .word {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-left: 5px;
  }
}

// 左侧内容label宽度
/deep/ .van-cell__title.van-field__label {
  width: 120px;
}

// 上传文件位置
/deep/ .van-field__control.van-field__control--custom {
  justify-content: flex-end;
}

.word_color {
  color: var(--primary-color);
}

.van-checkbox {
  font-size: 14px;
  color: #333;
}

.btn-box {
  padding: 10px 15px;

  .van-button {
    margin-bottom: 10px;
  }
}
</style>
