<template>
  <div class="insured-immediately">
    <van-sticky>
      <van-nav-bar title="保单填写" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-form @submit="onSubmit">
      <!-- 投保人信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">投保人信息</span>
          </template>
        </van-cell>
        <van-field
          required
          v-model="form.contact_name"
          name="姓名"
          placeholder="请填写姓名"
          :rules="[{ required: true, message: '请填写姓名' }]"
        >
          <template #label>
            <span>姓名</span>
          </template>
        </van-field>
        <van-field
          required
          v-model="form.contact_phone"
          name="联系方式"
          placeholder="请填写联系电话"
          :rules="[{ required: true, message: '请填写联系电话' }]"
        >
          <template #label>
            <span>联系电话</span>
          </template>
        </van-field>
      </div>
      <!-- 单位信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">单位信息</span>
          </template>
        </van-cell>

        <van-field
          required
          v-model="form.policyholder"
          name="投保单位名称"
          placeholder="请填写投保单位名称"
          :rules="[{ required: true, message: '需填写投保单位名称' }]"
        >
          <template #label>
            <span>投保单位名称</span>
          </template>
        </van-field>
        <van-field
          required
          v-model="form.policyholder_idcard_no"
          name="投保单位社会编码"
          placeholder="请填写投保单位社会编码"
          :rules="[{ required: true, message: '需填写投保单位社会编码' }]"
        >
          <template #label>
            <span>投保单位社会编码</span>
          </template>
        </van-field>
        <van-field
          required
          v-model="form.insured"
          name="被保单位名称"
          placeholder="请填写被保单位名称"
          :rules="[{ required: true, message: '需填写被保单位名称' }]"
        >
          <template #label>
            <span>被保单位名称</span>
          </template>
        </van-field>

        <van-field
          required
          v-model="form.insured_idcard_no"
          name="被保单位社会编码"
          placeholder="请填写被保单位社会编码"
          :rules="[{ required: true, message: '需填写被保单位社会编码' }]"
        >
          <template #label>
            <span>被保单位社会编码</span>
          </template>
        </van-field>
        <van-field
          required
          name="被保单位营业执照"
          placeholder="请上传被保单位营业执照"
          :rules="[{ required: true, message: '请上传被保单位营业执照' }]"
        >
          <template #label>
            <div>被保单位营业执照</div>
            <div style="font-size: 10px; white-space: wrap" class="color-primary">
              (jpeg, png, pdf, jpg, zip 文件，大小不能超过 4M）
            </div>
          </template>
          <template #input>
            <van-uploader v-model="form.business_license_file" :max-count="1" accept="*" :max-size="4 * 1024 * 1024">
              <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
            </van-uploader>
          </template>
        </van-field>
      </div>
      <!-- 保单相关信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">保单相关信息</span>
          </template>
        </van-cell>
        <van-field
          required
          clickable
          name="datetimePicker"
          v-model="form.start_at"
          placeholder="点击选择时间"
          @click="showCalendar = true"
          :rules="[{ required: true, message: '请选择投保时间' }]"
        >
          <template #label>
            <span>起保日期</span>
          </template>
        </van-field>
        <van-calendar
          :color="primaryColor"
          title="起保日期"
          v-model="showCalendar"
          :min-date="minDate"
          :max-date="new Date(2050, 0, 31)"
          @confirm="onConfirm"
        />
        <van-field
          required
          v-model="form.end_at"
          name="终保日期"
          placeholder="请选择起保日期"
          :rules="[{ required: true, message: '请选择投保时间' }]"
          readonly
        >
          <template #label>
            <span>终保日期</span>
          </template>
        </van-field>

        <!-- 投保单文件 -->
        <van-field required :rules="[{ required: true, message: '请上传投保单文件' }]">
          <template #label>
            <div>投保单文件</div>
            <div style="font-size: 10px; white-space: nowrap" color="color-primary">
              (doc, .docx, .zip, .rar 文件，大小不能超过 4M）
            </div>
          </template>
          <template #input>
            <van-uploader v-model="form.application_file" :max-count="1" accept="*" :max-size="4 * 1024 * 1024">
              <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
            </van-uploader>
          </template>
        </van-field>
        <!-- 投保单盖章文件 -->
        <van-field required :rules="[{ required: true, message: '请上传投保单盖章文件' }]">
          <template #label>
            <div>投保单盖章文件</div>
            <div style="font-size: 10px; white-space: nowrap" class="color-primary">
              ( jpeg, png, jpg, pdf, zip 文件，大小不能超过 4M）
            </div>
          </template>
          <template #input>
            <van-uploader v-model="form.application_stamp_file" :max-count="1" accept="*" :max-size="4 * 1024 * 1024">
              <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
            </van-uploader>
          </template>
        </van-field>
        <!-- 保险公司人员清单 -->
        <van-field required :rules="[{ required: true, message: '请上传保险公司人员清单' }]">
          <template #label>
            <div>保险公司人员清单</div>
            <div style="font-size: 10px; white-space: nowrap" class="color-primary">
              (xls, xlsx, zip文件，大小不能超过 4M）
            </div>
          </template>
          <template #input>
            <van-uploader v-model="form.staff_list_file" :max-count="1" accept="*" :max-size="4 * 1024 * 1024">
              <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
            </van-uploader>
          </template>
        </van-field>
        <!-- 人员清单盖章文件 -->
        <van-field required :rules="[{ required: true, message: '请上传人员清单盖章文件' }]">
          <template #label>
            <div>人员清单盖章文件</div>
            <div style="font-size: 10px; white-space: nowrap" class="color-primary">
              ( jpeg, png, jpg, pdf, zip 文件，大小不能超过 4M）
            </div>
          </template>
          <template #input>
            <van-uploader v-model="form.staff_stamp_list_file" :max-count="1" accept="*" :max-size="4 * 1024 * 1024">
              <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
            </van-uploader>
          </template>
        </van-field>
        <!-- 委托书 -->
        <van-field>
          <template #label>
            <div>委托书</div>
            <div style="font-size: 10px; color: ; white-space: nowrap" class="color-primary">
              ( .doc, .docx, .pdf 文件，大小不能超过 4M）
            </div>
          </template>
          <template #input>
            <van-uploader v-model="form.authorization_file" :max-count="1" accept="*" :max-size="4 * 1024 * 1024">
              <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
            </van-uploader>
          </template>
        </van-field>
        <!-- 其他文件 -->
        <van-field>
          <template #label>
            <div>其他文件</div>
            <div style="font-size: 10px; white-space: nowrap" class="color-primary">
              ( .doc, .docx, .pdf 文件，大小不能超过 4M）
            </div>
          </template>
          <template #input>
            <van-uploader v-model="form.extra_file" :max-count="1" accept="*" :max-size="4 * 1024 * 1024">
              <van-button icon="plus" :color="primaryColor" size="small">上传文件</van-button>
            </van-uploader>
          </template>
        </van-field>
      </div>
      <!-- 发票相关信息 -->
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">发票相关信息</span>
          </template>
        </van-cell>
        <van-field
          readonly
          clickable
          :value="form.invoice_type"
          label="发票类型"
          placeholder="点击选择发票类型"
          @click="showPicker2 = true"
        />
        <van-popup v-model="showPicker2" position="bottom">
          <van-picker show-toolbar :columns="columns" @confirm="exchange_type" @cancel="showPicker2 = false" />
        </van-popup>
        <div class="invoice" v-if="is_show">
          <van-field
            required
            v-model="form.invoice_title"
            placeholder="请填写发票抬头"
            :rules="[{ required: true, message: '需填写发票抬头' }]"
          >
            <template #label>
              <span>发票抬头</span>
            </template>
          </van-field>
          <van-field
            required
            v-model="form.invoice_tax_no"
            placeholder="请填写纳税人识别号"
            :rules="[{ required: true, message: '需填写纳税人识别号' }]"
          >
            <template #label>
              <span>纳税人识别号</span>
            </template>
          </van-field>
          <van-field
            required
            v-model="form.invoice_bank_name"
            placeholder="请填写开户行"
            :rules="[{ required: true, message: '需填写开户行' }]"
            v-if="invoice_type === 'special'"
          >
            <template #label>
              <span>开户行</span>
            </template>
          </van-field>
          <van-field
            required
            v-model="form.invoice_bankcard_no"
            placeholder="请填写账号"
            :rules="[{ required: true, message: '需填写账号' }]"
            v-if="invoice_type === 'special'"
          >
            <template #label>
              <span>账号</span>
            </template>
          </van-field>
          <van-field
            required
            v-model="form.invoice_registered_addr"
            placeholder="请填写公司地址"
            :rules="[{ required: true, message: '需填写公司地址' }]"
            v-if="invoice_type === 'special'"
          >
            <template #label>
              <span>公司地址</span>
            </template>
          </van-field>
          <van-field
            required
            v-model="form.invoice_phone_number"
            placeholder="请填写公司电话"
            :rules="[{ required: true, message: '需填写公司电话' }]"
            v-if="invoice_type === 'special'"
          >
            <template #label>
              <span>公司电话</span>
            </template>
          </van-field>
        </div>
        <van-cell>
          <template #title>
            <van-checkbox v-model="form.check" :checked-color="primaryColor"
              >我已详细阅读<span @click="showNotice = true" class="word_color">投保须知及除外事项</span>的内容
            </van-checkbox>
          </template>
        </van-cell>
        <van-popup
          v-model="showNotice"
          position="bottom"
          :style="{ height: '50%', padding: '10px', BoxSizing: 'border-box' }"
          round
          closeable
        >
          <h3>投保须知</h3>
          <div v-html="this.product?.additional?.notice"></div>
        </van-popup>
        <div class="btn-box">
          <van-button :disabled="!form.check" block :color="primaryColor" size="small" native-type="submit">
            暂存，下一步
          </van-button>
        </div>
      </div>
    </van-form>
    <div class="item-wrapper"></div>
  </div>
</template>

<script>
import { gzsubmitFrom } from '@/apis/policy'
import { getGroupProduct } from '@/apis/product'
import dayjs from 'dayjs'
import { insuredPolicyForm } from '@/apis/policy'

export default {
  name: 'insuredImmediately',
  data() {
    return {
      // columns: ['普票', '专票'],
      product: null,
      showCalendar: false,
      showCalendar1: false,
      showPicker2: false,
      showNotice: false,
      uploader: '',
      invoice_type: 'none',
      minDate: (() => {
        const today = new Date()
        const tomorrow = new Date(today)
        tomorrow.setDate(today.getDate() + 1)

        return tomorrow
      })(),
      form: {
        invoice_type: '不开发票'
      },
      columns: [
        { text: '不开发票', value: 'none' },
        { text: '普票', value: 'plain' },
        { text: '专票', value: 'special' }
      ],
      is_show: false
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  watch: {
    'form.invoice_type': {
      handler(newN, oldN) {
        if (newN !== oldN) {
          if (newN === '不开发票' || newN === '') {
            this.is_show = false
          } else {
            this.is_show = true
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.fetchGroupProductDetail()
  },
  methods: {
    fetchGroupProductDetail() {
      if (this.$route.query.product_id) {
        const productId = isNaN(parseInt(this.$route.query.product_id)) ? 0 : parseInt(this.$route.query.product_id)

        if (productId) {
          getGroupProduct(productId).then((r) => {
            this.product = r.data.data
          })
        }
      }

      if (this.$route.query.product_id) {
        const policyGroupId = isNaN(parseInt(this.$route.query.policy_group_id))
          ? 0
          : parseInt(this.$route.query.policy_group_id)

        if (policyGroupId) {
          insuredPolicyForm(policyGroupId).then((r) => {
            const policyForm = r.data.data.form
            const types = {
              none: '不开发票',
              normal: '普票',
              special: '专票'
            }

            this.form = Object.assign({}, this.form, policyForm)
            this.invoice_type = types[policyForm.invoice_type]
            this.form.invoice_type = types[policyForm.invoice_type]
          })
        }
      }
    },
    onClickLeft() {
      this.$router.go(-1)
    },
    // 起保时间
    onConfirm(date) {
      this.form.start_at = dayjs(date).format('YYYY-MM-DD 00:00:00')
      this.showCalendar = false

      this.form.end_at = dayjs(date).add(1, 'year').add(-1, 'day').format('YYYY-MM-DD 23:59:59')
    },
    // 发票
    onConfirm_2(value) {
      this.form.type = value
      this.showPicker2 = false
    },
    onSubmit(values) {
      const _obj = JSON.parse(window.sessionStorage.getItem('gz-write'))
      const _temp = Object.assign({}, this.form, _obj)
      for (const k in _temp) {
        if (_temp[k] instanceof Array) {
          _temp[k] = _temp[k][0].file
        }
      }
      _temp.invoice_type = this.invoice_type
      gzsubmitFrom(_temp).then((r) => {
        const _id = r.data.data.policy_group.id
        this.$router.push({ name: 'GroupPeopleList', params: { id: _id } })
      })
    },

    exchange_type(value) {
      this.form.invoice_type = value.text
      this.invoice_type = value.value
      this.showPicker2 = false
    }
  }
}
</script>

<style lang="less" scoped>
.color-primary {
  color: var(--primary-color);
}

.insured-immediately {
  min-height: 100vh;
  background-color: #ededed;

  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }

  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
  }
}

// 必填项
.required::after {
  content: '*';
  color: red;
}

// 保单填写
.van-cell {
  img {
    width: 12px;
    height: 15px;
    background-size: contain;
    line-height: 15px;
  }

  .word {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-left: 5px;
  }
}

// 左侧内容label宽度
/deep/ .van-cell__title.van-field__label {
  width: 120px;
}

// 上传文件位置
/deep/ .van-field__control.van-field__control--custom {
  justify-content: flex-end;
}

.word_color {
  color: var(--primary-color);
}

.van-checkbox {
  font-size: 14px;
  color: #333;
}

.btn-box {
  padding: 10px 15px;

  .van-button {
    margin-bottom: 10px;
  }
}
</style>
