<template>
  <van-popup v-model="popupVisible" position="bottom">
    <van-picker
      show-toolbar
      title="请选择币种"
      :columns="currencies"
      :default-index="defaultIndex"
      @confirm="handleConfirm"
      @cancel="popupVisible = false"
    />
  </van-popup>
</template>

<script>
import { getCurrencies } from '@/apis/currency'

export default {
  name: 'CurrencySelector',
  props: {
    value: {
      type: [Number, String],
      default: -1
    },
    visible: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    filedText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      popupVisible: false,
      currencies: []
    }
  },
  watch: {
    value(value, oldValue) {
      if (value && (oldValue === '' || oldValue === -1) && this.$route.query.from !== undefined) {
        this.fetchCurrencies()
      }
    },
    visible(value) {
      if (value !== this.popupVisible && !this.disabled) {
        this.popupVisible = value
      }
    },
    popupVisible(value) {
      if (value !== this.visible) {
        this.$emit('update:visible', value)
      }
    },
    defaultIndex() {
      this.$emit('update:fieldText', this.currencies?.[this.defaultIndex]?.text)
    }
  },
  computed: {
    defaultIndex() {
      return this.currencies.findIndex((e) => e.id === this.value)
    }
  },
  created() {
    if (this.$route.query.from === undefined || this.$route.query.from === 'copy') {
      this.fetchCurrencies()
    }
  },
  methods: {
    handleConfirm(value) {
      this.$emit('input', value.id)
      this.$emit('update:fieldText', value.name)
      this.$emit('update:currency', Object.assign({}, value))

      this.popupVisible = false
    },
    fetchCurrencies() {
      getCurrencies({
        with: this.value
      }).then((r) => {
        const data = r.data.data

        data.map((e) => {
          e.text = `${e.name}(${e.rate})`
          return e
        })

        this.currencies = data
      })
    }
  }
}
</script>
