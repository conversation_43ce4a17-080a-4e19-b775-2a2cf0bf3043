<template>
  <van-row>
    <van-col :span="24">
      <van-field
        readonly
        clickable
        show-toolbar
        label="标的"
        class="required"
        placeholder="请选择标的"
        right-icon="arrow"
        :value="subject.node.name"
        :disabled="isDisabled"
        @click="subject.visible = true"
      />
      <van-popup v-model="subject.visible" round position="bottom" :lazy-render="false">
        <van-picker
          title="标的"
          ref="subjectPicker"
          show-toolbar
          :columns="subject.columns"
          :default-index="subject.defaultIndex"
          @cancel="subject.visible = false"
          @confirm="updateSubjectId"
        />
      </van-popup>
    </van-col>
    <van-col :span="24">
      <van-field
        name="radio"
        label="是否为二手货/旧器材/旧货、已受损货物、退运货物、进口转运货（释意：进口货物连续进行国内运输但仅计划投保国内运输段）"
      >
        <template #input>
          <van-radio-group v-model="isActualNew" direction="horizontal" :disabled="isDisabled">
            <van-radio :name="1">否</van-radio>
            <van-radio :name="0">是</van-radio>
          </van-radio-group>
        </template>
      </van-field>
    </van-col>
    <van-col :span="24" v-if="subject.node.id === 3">
      <van-field label="人工审核原因" readonly />
      <van-cell>
        <van-checkbox-group v-model="subjectCategories" direction="horizontal">
          <van-checkbox
            v-for="category in subject.columns?.find((e) => e.id === subject.node.id)?.categories"
            :key="category.id"
            :name="category.id"
            :disabled="isDisabled"
          >
            {{ category.name }}
          </van-checkbox>
        </van-checkbox-group>
      </van-cell>
    </van-col>

    <van-col :span="24" v-if="subject.node.id === 3">
      <van-field label="承保费率条件" readonly />
      <van-field v-model="manualConditionsContent" placeholder="请输入承保费率条件" />
    </van-col>

    <template v-if="mainClauses.length > 0">
      <van-col :span="24">
        <van-field
          readonly
          clickable
          show-toolbar
          label="主险"
          class="required"
          placeholder="请选择主险"
          right-icon="arrow"
          :value="mainClause.node.name"
          :disabled="isDisabled"
          @click="mainClause.visible = true"
        />
        <van-popup v-model="mainClause.visible" round position="bottom" :lazy-render="false">
          <van-picker
            title="主险"
            ref="mainClausePicker"
            show-toolbar
            :columns="mainClauses"
            :default-index="mainClause.defaultIndex"
            @cancel="mainClause.visible = false"
            @confirm="updateMainClauseId"
          />
        </van-popup>
      </van-col>
      <van-col :span="24">
        <van-field label="附加险" readonly />
        <van-cell>
          <van-checkbox-group
            v-model="additionalClauseIds"
            v-if="additionalClauses.length > 0"
            direction="horizontal"
            :disabled="isDisabled"
          >
            <van-checkbox
              v-for="additionalClause in additionalClauses"
              :key="additionalClause.id"
              :name="additionalClause.id"
              :disabled="!mainClause.id"
            >
              {{ additionalClause.name }}
            </van-checkbox>
          </van-checkbox-group>
          <p class="b-alert" v-else>当前条件下无可投保附加险</p>
        </van-cell>
      </van-col>
      <van-col :span="24">
        <van-field label="出单公司" readonly />
        <van-cell>
          <template v-if="coms.length > 0">
            <div class="inline-radio">
              <van-radio
                v-for="com in coms"
                v-model="comId"
                :key="com.id"
                :name="com.id"
                :disabled="additionalClauseIds.length <= 0 || isDisabled"
              >
                {{ com.company_branch }}
              </van-radio>
            </div>
          </template>
          <p class="b-alert" v-else>当前条件下无可投保出单公司</p>
        </van-cell>
      </van-col>
      <van-col :span="24" v-if="product.id !== undefined">
        <van-collapse v-model="showContent" accordion>
          <van-collapse-item title="费用及其他信息" name="content">
            <van-cell title="费率" :value="product?.user_rate + '‱'"></van-cell>
            <van-cell title="最低保费" :value="product?.minimum_premium"></van-cell>
            <van-cell title="最高保险金额" :value="product?.additional?.coverage"></van-cell>
            <van-cell title="禁用区域" :value="product?.additional?.disabled_regions"></van-cell>
            <van-cell title="免赔" :label="product?.additional?.deductible"></van-cell>
            <van-cell title="特别约定" :label="product?.additional?.special_agreement"></van-cell>
            <template v-if="subjectId === 2">
              <van-cell title="是否为下列货物">
                <van-radio-group v-model="isExceptGoods" direction="horizontal" class="required">
                  <van-radio :name="true">是</van-radio>
                  <van-radio :name="false">否</van-radio>
                </van-radio-group>
              </van-cell>
              <van-cell title="" :label="product?.additional?.except_subject"></van-cell>
            </template>
            <template v-else>
              <van-cell title="除外货物" :label="product?.additional?.except_subject"></van-cell>
            </template>
          </van-collapse-item>
        </van-collapse>
      </van-col>
    </template>
    <template v-else>
      <van-col :span="24" v-if="subject.id">
        <van-empty image="error" description="您当前暂无可投保产品" />
      </van-col>
    </template>
  </van-row>
</template>

<script>
import { getSubjects } from '@/apis/subject'
import { getProducts } from '@/apis/product'
import { arraysEqual } from '@/utils/func'
import { Dialog } from 'vant'

const PRODUCT_TYPE = 1

export default {
  name: 'DomesticProduct',
  props: {
    productId: {
      type: [Number, String],
      default: -1
    },
    subjectId: {
      type: [Number, String],
      default: -1
    },
    subjectCategoryIds: {
      type: Array,
      default: []
    },
    isNew: {
      type: [Number, Boolean, String],
      default: 1
    },
    manualConditions: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      products: [],
      showContent: 'content',
      isActualNew: 1,
      isExceptGoods: null,
      subject: {
        defaultIndex: 0,
        visible: false,
        columns: [],
        node: {
          name: ''
        },
        id: ''
      },
      subjectCategories: [],
      manualConditionsContent: '',
      mainClause: {
        defaultIndex: 0,
        visible: false,
        node: {
          name: ''
        },
        id: ''
      },
      additionalClauseIds: [],
      comId: '',
      defaultValue: {
        defaultIndex: 0,
        visible: false,
        node: {
          company_branch: '',
          name: ''
        },
        id: ''
      }
    }
  },
  computed: {
    isDisabled() {
      return this.$route.query.from === 'edit'
    },
    mainClauses() {
      if (parseInt(this.isNew, 10) === 0) {
        return (
          this.products?.main_clauses
            ?.filter((e) => e.is_allow_used)
            ?.map((e) => {
              e.text = e.name
              return e
            }) || []
        )
      }

      return (
        this.products?.main_clauses?.map((e) => {
          e.text = e.name
          return e
        }) || []
      )
    },
    additionalClauses() {
      const additionalClauses = this.products.additional_clauses
      // 如果主险为空 显示所有附加险
      if (!this.mainClause.id) {
        return additionalClauses
      }

      const mainClause = this.mainClauses.find((e) => e.id === this.mainClause.id)

      return additionalClauses.filter((item) => item.parent_ids.some((r) => mainClause.ids.includes(r))) || []
    },
    // 保险公司
    coms() {
      const products = this.products.products
      // 如果主险为空 显示所有保险公司, 并去重
      if (!this.mainClause.id && this.additionalClauseIds.length <= 0) {
        return products.filter(
          (value, index) => index === products.findIndex((e) => e.company_branch === value.company_branch)
        )
      }

      // 如果主险不为空 附加险为空 显示当前主险下的所有保险公司
      if (this.mainClause.id && this.additionalClauseIds.length <= 0) {
        let coms = products.filter((item) => item.new_main_clause_id === this.mainClause.id) || []
        coms = coms.filter((value, index) => index === coms.findIndex((e) => e.company_branch === value.company_branch))

        return coms
      }

      // 如果附加险不为空 显示当前附加险下的所有保险公司
      if (this.additionalClauseIds.length > 0) {
        return (
          products.filter(
            (item) =>
              arraysEqual(item.new_additional_clause_ids, this.additionalClauseIds) &&
              item.new_main_clause_id === this.mainClause.id
          ) || []
        )
      }

      return products
    },
    product() {
      return this.products?.products?.find((e) => e.id === this.comId) || {}
    }
  },
  watch: {
    isExceptGoods(value) {
      if (value) {
        Dialog.alert({
          title: '提示',
          message: '请正确选择货物类别投保或联系业务人员',
          theme: 'round-button'
        })
      }

      this.$emit('update:isExceptGoods', value)
    },
    subjectId(value) {
      if (value !== 2 && value !== -1) {
        this.isExceptGoods = false
      }

      this.fetchSubjects()
    },
    productId(value, oldValue) {
      if (this.subjectId !== -1 && value !== oldValue && value !== this.comId) {
        this.fetchProducts(this.subjectId)
      }
    },
    comId: {
      deep: true,
      immediate: true,
      handler(value, oldValue) {
        if (value !== oldValue) {
          this.$emit('change', this.product)
          if (value !== '' && value !== -1) {
            this.$emit('update:productId', value)
          }
        }
      }
    },
    isActualNew(value) {
      this.$emit('update:is-new', value)
    },
    isNew(value) {
      if (value !== this.isActualNew) {
        this.isActualNew = value
      }

      this.resetMainClause()
      this.resetAdditionalClause()
      this.resetCom()
    },
    'mainClause.id'(value, oldValue) {
      if (oldValue !== '' && value !== oldValue) {
        this.resetAdditionalClause()
        this.resetCom()
      }
    },
    additionalClauseIds: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        if (oldValue !== undefined && newValue !== undefined) {
          const newNoneIdx = newValue.findIndex((e) => parseInt(e, 10) <= 0)
          const oldNoneIdx = oldValue.findIndex((e) => parseInt(e, 10) <= 0)
          // 如果是新选择的无删除其他附件.
          if (newNoneIdx !== -1 && oldNoneIdx === -1) {
            this.additionalClauseIds = [newValue[newNoneIdx]]
          }

          if (oldNoneIdx !== -1 && newNoneIdx !== -1 && newValue.length > 1) {
            delete newValue[newNoneIdx]
            this.additionalClauseIds = newValue.filter((e) => e)
          }
        }

        if (oldValue !== undefined && oldValue?.length > 0 && !arraysEqual([...newValue], [...oldValue])) {
          this.resetCom()
        }
      }
    },
    'subject.id'(value) {
      this.resetMainClause()
      this.resetAdditionalClause()
      this.resetCom()

      this.$emit('update:subjectId', value)
    },
    subjectCategories(value) {
      this.$emit('update:subjectCategoryIds', value)
    },
    subjectCategoryIds(value) {
      if (JSON.stringify(value) !== JSON.stringify(this.subjectCategories)) {
        this.subjectCategories = value
      }
    },
    manualConditionsContent(value) {
      this.$emit('update:manualConditions', value)
    },
    manualConditions(value) {
      if (value !== this.manualConditionsContent) {
        this.manualConditionsContent = value
      }
    }
  },
  created() {
    this.fetchSubjects()

    if ((this.subjectId !== 2 && this.subjectId !== -1) || ['edit', 'continue'].includes(this.$route.query.from)) {
      this.isExceptGoods = false
    }

    this.subjectCategories = this.subjectCategoryIds
    this.manualConditionsContent = this.manualConditions

    this.$emit('update:isExceptGoods', this.isExceptGoods)
  },
  methods: {
    resetMainClause() {
      if (this.$refs.mainClausePicker !== undefined) {
        this.$refs.mainClausePicker.setColumnIndex('')
        this.$refs.mainClausePicker.setColumnValue('')
      }

      this.mainClause = Object.assign({}, this.defaultValue)
    },
    resetAdditionalClause() {
      this.additionalClauseIds = []
    },
    resetCom() {
      this.comId = ''
    },
    fetchProducts(subjectId) {
      getProducts(
        {
          type: PRODUCT_TYPE,
          subject_id: subjectId
        },
        {
          id: this.$route.query.from === 'edit' ? this.productId : ''
        }
      ).then((r) => {
        this.products = r.data.data

        if (this.productId && this.productId !== -1) {
          // com
          this.products.products.forEach((e) => {
            if (e.id === this.productId) {
              this.comId = this.productId
              this.additionalClauseIds = e.new_additional_clause_ids

              this.mainClauses.forEach((mc, idx) => {
                if (mc.id === e.new_main_clause_id) {
                  this.mainClause.defaultIndex = idx
                  this.updateMainClauseId(mc)
                }
              })
            }
          })
        }
      })
    },
    fetchSubjects() {
      getSubjects().then((r) => {
        const [...data] = [...r.data.data]

        let subject = { idx: 0, value: {} }
        data.forEach((item, index) => {
          item.text = item.name
          if (item.id === this.subjectId) {
            subject = {
              idx: index,
              value: item
            }
          }
        })

        this.subject.columns = data

        if (this.subjectId !== this.subject.id && this.subjectId !== -1) {
          this.subject.id = this.subjectId

          // ??? Not working.
          this.$refs.subjectPicker.setColumnValue(subject.idx, subject.value)
          this.$refs.subjectPicker.setColumnIndex(subject.idx)

          // Hack: prop subjectId
          this.subject.defaultIndex = subject.idx
          this.subject.node = subject.value
          this.subject.id = subject.value.id
          this.subject.visible = false
        }
      })
    },
    updateSubjectId(node) {
      this.subject.node = node
      this.subject.id = node.id
      this.subject.visible = false

      this.subjectCategories = []

      this.fetchProducts(node.id)
    },
    updateMainClauseId(node) {
      this.mainClause.node = node
      this.mainClause.id = node.id
      this.mainClause.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .van-field__control.van-field__control--custom {
  justify-content: flex-start !important;
}

.required::after {
  content: '*' !important;
  color: red;
  left: 3.5rem;
  font-size: 18px;
  bottom: auto;
  transform: none;
}

.b-alert {
  background-color: #fdf6ec;
  padding: 10px;
  color: #e6a23c;
}

.inline-radio {
  .van-radio {
    display: inline-flex;
    margin-right: 0.32rem;
  }
}
</style>
