<template>
  <van-picker
    title="国家/地区"
    :columns="columns"
    :default-index.sync="defaultIndex"
    show-toolbar
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
</template>

<script>
import regions from '@/utils/regions.js'

export default {
  name: 'CountryRegionSelector',
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Array,
      default: () => []
    },
    only: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      regions,
      selected: ''
    }
  },
  computed: {
    columns() {
      const columns = []
      Object.keys(this.regions).forEach((value) => {
        if (!this.disabled.includes(value)) {
          if (this.only.length > 0 && !this.only.includes(value)) {
            return
          }

          columns.push({
            value: value,
            text: this.regions[value]
          })
        }
      })

      return columns
    },
    defaultIndex() {
      return this.columns.findIndex((column) => column.value === this.value)
    }
  },
  methods: {
    handleConfirm(value) {
      this.$emit('confirm', value)
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>
