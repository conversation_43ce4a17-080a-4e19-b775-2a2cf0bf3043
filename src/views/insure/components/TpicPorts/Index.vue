<template>
  <div>
    <van-field
      readonly
      clickable
      :value="port"
      :placeholder="`太平保险请务必选择正确的${label}港口`"
      @click="showPicker = true"
      right-icon="arrow"
      :class="{ 'required-asterisk': isRequired }"
      :label="`${label}港口`"
      :rules="[{ required: isRequired, message: '此项为必填项' }]"
    >
    </van-field>
    <!-- 弹出层 -->
    <van-popup v-model="showPicker" position="bottom">
      <van-field v-model="searchKeyword" placeholder="输入关键字搜索"/>
      <van-picker show-toolbar :columns="columns" @confirm="clickPopup" :default-index="defaultIndex" @cancel="showPicker = false" />
    </van-popup>
  </div>
</template>

<script>
import taipingPorts from './tpic-ports'

export default {
  name: 'TpicPorts',
  props: {
    isRequired: {
      type: Boolean,
      default: true
    },
    label: {
      type: String,
      default: ''
    },
    suggestion: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      taipingPorts,
      defaultIndex: 0,
      showPicker: false,
      port: '',
      searchKeyword: '',
    }
  },
  computed: {
    columns() {
      const columns = this.taipingPorts.map(item => {
        return {
          text: item.txt,
          id: item.txt
        }
      })

      if (this.searchKeyword) {
        return columns.filter(item => item.text.indexOf(this.searchKeyword) > -1)
      }

      return columns
    },
  },
  watch: {
    value(value) {
      this.port = value
      const taipingPortIdx = this.taipingPorts.findIndex((p) => p.txt ===  value)
      if (taipingPortIdx !== -1) {
        this.defaultIndex = taipingPortIdx
        this.port = this.taipingPorts[taipingPortIdx].txt
      }
    },
    suggestion(value) {
      if (value) {
        const suggestionAddr = value.split('-')[1].slice(0, 2)
        const taipingPortIdx = this.taipingPorts.findIndex((p) => p.txt.indexOf(suggestionAddr) > -1)
        if (taipingPortIdx !== -1) {
          this.defaultIndex = taipingPortIdx
          this.port = this.taipingPorts[taipingPortIdx].txt
        }
      }
    },
  },
  created() {
    if (this.value) {
      this.port = this.value
      const taipingPortIdx = this.taipingPorts.findIndex((p) => p.txt ===  this.value)
      if (taipingPortIdx !== -1) {
        this.defaultIndex = taipingPortIdx
        this.port = this.taipingPorts[taipingPortIdx].txt
      }
    }
  },
  methods: {
    clickPopup(value) {
      this.$emit('input', value.id)
      this.port = value.text
      this.showPicker = false
    }
  }
}
</script>

<style lang="less" scoped>
// 必填项
.required-asterisk::after {
  z-index: 9;
  content: '*' !important;
  color: red;
  left: 3.5rem;
  font-size: 18px;
  bottom: auto;
  transform: none;
}
</style>
