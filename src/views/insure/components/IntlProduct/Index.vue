<template>
  <van-row>
    <van-col :span="24">
      <van-field
        readonly
        clickable
        show-toolbar
        class="required"
        label="标的"
        placeholder="请选择标的"
        right-icon="arrow"
        :value="subject.node.name"
        @click="subject.visible = true"
        :disabled="isDisabled"
      />
      <van-popup v-model="subject.visible" round position="bottom" :lazy-render="false">
        <van-picker
          title="标的"
          ref="subjectPicker"
          show-toolbar
          :columns="subject.columns"
          :default-index="subject.defaultIndex"
          @cancel="subject.visible = false"
          @confirm="updateSubjectId"
        />
      </van-popup>
    </van-col>
    <van-col :span="24" v-if="subject.node.id === 3">
      <van-field label="人工审核原因" readonly />
      <van-cell>
        <van-checkbox-group v-model="subjectCategories" direction="horizontal">
          <van-checkbox
            v-for="category in subject.columns?.find((e) => e.id === subject.node.id)?.categories"
            :key="category.id"
            :name="category.id"
            :disabled="isDisabled"
          >
            {{ category.name }}
          </van-checkbox>
        </van-checkbox-group>
      </van-cell>
    </van-col>
    <van-col :span="24" v-if="subject.node.id === 3">
      <van-field label="承保费率条件" readonly />
      <van-field v-model="manualConditionsContent" placeholder="请输入承保费率条件" />
    </van-col>

    <template v-if="coms.length >= 0">
      <van-col :span="24">
        <van-field
          readonly
          clickable
          show-toolbar
          label="出单公司"
          class="required"
          placeholder="请选择出单公司"
          right-icon="arrow"
          :value="com.node.company_branch"
          :disabled="isDisabled"
          @click="com.visible = true"
        />
        <van-popup v-model="com.visible" round position="bottom" :lazy-render="false" v-if="coms.length > 0">
          <van-picker
            title="出单公司"
            ref="comPicker"
            show-toolbar
            :columns="coms"
            :default-index="com.defaultIndex"
            @cancel="com.visible = false"
            @confirm="updateComId"
          />
        </van-popup>
      </van-col>
      <van-col :span="24" v-if="product.id !== undefined">
        <van-collapse v-model="showContent" accordion>
          <van-collapse-item title="费用及其他信息" name="content">
            <van-cell title="费率" :value="product.user_rate + '‱'"></van-cell>
            <van-cell title="最低保费" :value="product.minimum_premium"></van-cell>
            <van-cell title="最高保险金额" :value="product.additional.coverage"></van-cell>
            <van-cell title="禁用区域" :value="product.additional.disabled_regions"></van-cell>
            <van-cell title="免赔" :label="product.additional.deductible"></van-cell>
            <van-cell title="特别约定" :label="product.additional.special_agreement"></van-cell>
            <template v-if="subjectId === 2">
              <van-cell title="是否为下列货物">
                <van-radio-group v-model="isExceptGoods" direction="horizontal" class="required">
                  <van-radio :name="true">是</van-radio>
                  <van-radio :name="false">否</van-radio>
                </van-radio-group>
              </van-cell>
              <van-cell title="" :label="product?.additional?.except_subject"></van-cell>
            </template>
            <template v-else>
              <van-cell title="除外货物" :label="product?.additional?.except_subject"></van-cell>
            </template>
          </van-collapse-item>
        </van-collapse>
      </van-col>
    </template>
    <template v-else>
      <van-col :span="24" v-if="subject.id">
        <van-empty image="error" description="您当前暂无可投保产品" />
      </van-col>
    </template>
  </van-row>
</template>

<script>
import { getSubjects } from '@/apis/subject'
import { getProducts } from '@/apis/product'
import { Dialog } from 'vant'

const PRODUCT_TYPE = 2

export default {
  name: 'IntlProduct',
  props: {
    productId: {
      type: [Number, String],
      default: -1
    },
    subjectId: {
      type: [Number, String],
      default: -1
    },
    subjectCategoryIds: {
      type: Array,
      default: []
    },
    manualConditions: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      products: [],
      showContent: 'content',
      isExceptGoods: null,
      subject: {
        defaultIndex: 0,
        visible: false,
        columns: [],
        node: {
          name: ''
        },
        id: ''
      },
      subjectCategories: [],
      manualConditionsContent: '',
      com: {
        defaultIndex: 0,
        visible: false,
        node: {
          company_branch: ''
        },
        id: ''
      },
      defaultValue: {
        defaultIndex: 0,
        visible: false,
        node: {
          company_branch: '',
          name: ''
        },
        id: ''
      }
    }
  },
  computed: {
    isDisabled() {
      return this.$route.query.from === 'edit'
    },
    coms() {
      return (
        this.products?.map((e) => {
          e.text = e.company_branch
          return e
        }) || []
      )
    },
    product() {
      return this.products?.find((e) => e.id === this.com.id) || {}
    }
  },
  watch: {
    subjectId(value) {
      this.fetchSubjects()

      if (value !== 2 && value !== -1) {
        this.isExceptGoods = false
      }
    },
    productId(value, oldValue) {
      if (this.subjectId !== 1 && value !== oldValue && value !== this.com.id) {
        this.fetchProducts(this.subjectId)
      }
    },
    'com.id': {
      deep: true,
      immediate: true,
      handler(value) {
        this.$emit('change', this.product)

        if (value !== '' && value !== -1) {
          this.$emit('update:productId', value)
        }
      }
    },
    'subject.id'(value) {
      this.$emit('update:subjectId', value)
    },
    'subjectCategory.id'(value) {
      this.$emit('update:subjectCategoryId', value)
    },
    isExceptGoods(value) {
      if (value) {
        Dialog.alert({
          title: '提示',
          message: '请正确选择货物类别投保或联系业务人员',
          theme: 'round-button'
        })
      }

      this.$emit('update:isExceptGoods', value)
    },
    subjectCategories(value) {
      this.$emit('update:subjectCategoryIds', value)
    },
    subjectCategoryIds(value) {
      if (JSON.stringify(value) !== JSON.stringify(this.subjectCategories)) {
        this.subjectCategories = value
      }
    },
    manualConditionsContent(value) {
      this.$emit('update:manualConditions', value)
    },
    manualConditions(value) {
      if (value !== this.manualConditionsContent) {
        this.manualConditionsContent = value
      }
    }
  },
  created() {
    if ((this.subjectId !== 2 && this.subjectId !== -1) || ['edit', 'continue'].includes(this.$route.query.from)) {
      this.isExceptGoods = false
    }
    this.subjectCategories = this.subjectCategoryIds
    this.manualConditionsContent = this.manualConditions

    this.fetchSubjects()
  },
  methods: {
    fetchProducts(subjectId) {
      getProducts(
        {
          type: PRODUCT_TYPE,
          subject_id: subjectId
        },
        {
          id: this.$route.query.from === 'edit' ? this.productId : ''
        }
      ).then((r) => {
        this.products = r.data.data

        if (this.productId && this.productId !== -1) {
          // com
          this.products.forEach((e, idx) => {
            if (e.id === this.productId) {
              this.com.defaultIndex = idx
              this.updateComId(e)
            }
          })
        }
      })
    },
    fetchSubjects() {
      getSubjects().then((r) => {
        const [...data] = [...r.data.data]

        let subject = { idx: 0, value: {} }
        data.forEach((item, index) => {
          item.text = item.name
          if (item.id === this.subjectId) {
            subject = {
              idx: index,
              value: item
            }
          }
        })

        this.subject.columns = data

        if (this.subjectId !== this.subject.id && this.subjectId !== -1) {
          this.subject.id = this.subjectId

          // ??? Not working.
          this.$refs.subjectPicker.setColumnValue(subject.idx, subject.value)
          this.$refs.subjectPicker.setColumnIndex(subject.idx)

          // Hack: prop subjectId
          this.subject.defaultIndex = subject.idx
          this.updateSubjectId(subject.value)
        }
      })
    },
    updateSubjectId(node) {
      this.subject.node = node
      this.subject.id = node.id
      this.subject.visible = false

      this.subjectCategories = []

      this.fetchProducts(node.id)
    },
    updateComId(node) {
      this.com.node = node
      this.com.id = node.id
      this.com.visible = false
    },
    updateSubjectCategoryId(node) {
      this.subjectCategory.node = node
      this.subjectCategory.id = node.id
      this.subjectCategory.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .van-field__control.van-field__control--custom {
  justify-content: flex-start !important;
}

.required::after {
  content: '*' !important;
  color: red;
  left: 3.5rem;
  font-size: 18px;
  bottom: auto;
  transform: none;
}
</style>
