<template>
  <div class="manage">
    <van-sticky>
      <van-tabs v-model="active" sticky @click="onClick" :color="primaryColor">
        <van-tab v-for="item in titles" :title="item.name" :key="item.type" :name="item.type"></van-tab>
      </van-tabs>
      <van-search v-model="value" placeholder="搜索流水号/保单号/投保用户" @input="inputContent" />
    </van-sticky>
    <div class="wrapper">
      <p>
        <span class="total">保单数: {{ paging.total }}</span>
        <span class="dropdown" @click="changeList">{{ tableType }}<van-icon name="bars" /></span>
        <van-popup v-model="showPicker" position="bottom">
          <van-picker show-toolbar :columns="statuses" @confirm="onConfirm" @cancel="showPicker = false" />
        </van-popup>
      </p>
      <div class="card">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多啦" @load="onLoad">
          <van-cell-group v-for="item in data" :key="item.id" @click="goToDetail(item)">
            <van-cell>
              <template #title>
                <div v-if="!is_three && !is_four">
                  <span class="custom-title">{{ item.departure }}</span>
                  <van-icon name="down" class="arrowStatus" />
                  <span class="custom-title">{{ item.destination }}</span>
                </div>
                <div v-else>
                  <span>{{ item.product?.name }}</span>
                </div>
              </template>
              <template #right-icon>
                <!-- 已退回、未提交 -->
                <span class="status-style color1" v-if="item.status === 0 || item.status === 10">
                  {{ item.status_text }}
                </span>
                <!-- 已审核、已出单、已支付 -->
                <span class="status-style color2" v-if="item.status === 3 || item.status === 4 || item.status === 5">
                  {{ item.status_text }}
                </span>
                <!-- 已作废、退保 -->
                <span class="status-style color3" v-if="item.status === 6 || item.status === 7">
                  {{ item.status_text }}
                </span>
                <!-- 审核中、退保中、批改中 -->
                <span class="status-style color4" v-if="item.status === 2 || item.status === 8 || item.status === 9">
                  {{ item.status_text }}
                </span>
                <!-- 已提交、待确认、待支付、暂存单 -->
                <span class="status-style color5" v-if="item.status === 1 || item.status === 11 || item.status === 12">
                  {{ item.status_text }}
                </span>
              </template>
            </van-cell>
            <van-cell>
              <template #title>
                <span style="font-size: 18px; font-weight: bold; color: #333">
                  {{ item.policy_no ? item.policy_no : '尚未出单~' }}
                </span>
              </template>
            </van-cell>
            <van-field class="van-mini-space" :placeholder="item.order_no" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">流水号</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.insured" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">被保险人</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.submitted_at" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">投保时间</span>
              </template>
            </van-field>
            <van-field class="van-mini-space" :placeholder="item.user.name" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">投保用户</span>
              </template>
            </van-field>
            <div class="clickBtn">
              <van-button
                v-if="[1, 2, 3].includes(item.type)"
                size="small"
                plain
                round
                color="#999"
                @click.stop="gotoPage(item)"
              >
                复制投保
              </van-button>
              <van-button
                size="small"
                type="primary"
                round
                :color="primaryColor"
                v-if="item.status === 5"
                @click.stop="downloadFile(item)"
              >
                电子保单
              </van-button>
            </div>
          </van-cell-group>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script>
import { mainList, downloadFrom, gzList } from '@/apis/manage'
import { mapGetters } from 'vuex'

export default {
  name: 'Policies',
  data() {
    return {
      // 1:国内 2:国际 3:单车 4:通用 5:雇主
      title: [
        { type: 1, name: '国内货运险', feature: 'insure.domestic' },
        { type: 2, name: '国际货运险', feature: 'insure.intl' },
        { type: 5, name: '雇主责任险', feature: 'insure.group' },
        { type: 3, name: '单车责任险', feature: 'insure.lbt' },
        { type: 4, name: '其他险种', feature: 'insure.general' },
        { type: 6, name: '线下录入保单', feature: 'insure.offline' },
        { type: -1, name: '' }
      ],
      // active: 1,
      tableType: '全部保单',
      selectValue: undefined,
      showPicker: false,
      columns: ['全部保单', '已提交', '已出单'],
      active: 0,
      value: '',
      is_four: false,
      is_three: false,
      statuses: [
        { text: '暂存单', value: 0 },
        { text: '已提交', value: 1 },
        { text: '审核中', value: 2 },
        { text: '已支付', value: 3 },
        { text: '已审核', value: 4 },
        { text: '已出单', value: 5 },
        { text: '已退保', value: 6 },
        { text: '已作废', value: 7 },
        { text: '批改中', value: 8 },
        { text: '退保中', value: 9 },
        { text: '已退回', value: 10 },
        { text: '待确认', value: 11 },
        { text: '待支付', value: 12 },
        { text: '已退回(补充资料)', value: 13 }
      ],
      newName: 0,
      data: [],
      loading: false,
      finished: false,
      paging: {
        current: 0,
        total: 0
      }
    }
  },
  computed: {
    ...mapGetters(['features', 'primaryColor']),
    titles() {
      return this.title.filter((t) => this.features?.includes(t.feature))
    }
  },
  mounted() {
    if (this.$route.query.active) {
      this.active = isNaN(parseInt(this.$route.query.active)) ? 0 : parseInt(this.$route.query.active)
    }
    if (!this.title.map((t) => t.type).includes(this.active)) {
      this.active = 1
    }
  },
  methods: {
    onLoad() {
      this.paging.current++
      this.getPolicyList(this.active)
    },
    getList(data) {
      mainList(data).then((r) => {
        this.data.push(...r.data.data)
        this.loading = false
        this.paging.current = r.data.meta.current_page
        this.paging.total = r.data.meta.total
        this.paging.last_page = r.data.meta.last_page

        if (this.paging.current >= r.data.meta.last_page) {
          this.finished = true
        }
      })
    },
    inputContent() {
      this.data = []
      this.finished = false
      this.paging = {
        current: 1,
        total: 0
      }

      if (this.newName !== 5) {
        this.getList({
          filter: { type: this.newName, status: this.selectValue, search: this.value },
          page: this.paging.current
        })
      } else {
        gzList({
          filter: { type: this.newName, status: this.selectValue, search: this.value },
          page: this.paging.current
        }).then((r) => {
          this.data.push(...r.data.data)
          this.loading = false
          this.paging.current = r.data.meta.current_page
          this.paging.total = r.data.meta.total
          this.paging.last_page = r.data.meta.last_page

          if (this.paging.current >= r.data.meta.last_page) {
            this.finished = true
          }
        })
      }
    },
    changeList() {
      this.showPicker = true
    },
    onConfirm(value) {
      this.data = []
      this.finished = false
      this.paging = {
        current: 1,
        total: 0
      }

      this.tableType = value.text
      this.selectValue = value.value
      this.showPicker = false
      if (this.newName !== 5) {
        this.getList({
          filter: { type: this.newName, status: value.value, search: this.value },
          page: this.paging.current
        })
      } else {
        gzList({
          filter: { type: this.newName, status: value.value, search: this.value },
          page: this.paging.current
        }).then((r) => {
          this.data.push(...r.data.data)
          this.loading = false
          this.paging.current = r.data.meta.current_page
          this.paging.total = r.data.meta.total
          this.paging.last_page = r.data.meta.last_page

          if (this.paging.current >= r.data.meta.last_page) {
            this.finished = true
          }
        })
      }
    },
    onClick(name, title) {
      this.data = []
      this.finished = false
      this.paging = {
        current: 1,
        total: 0
      }

      this.getPolicyList(name)
    },
    getPolicyList(name) {
      this.newName = name
      if (name !== 5) {
        if (name === 4) {
          this.is_four = true
        } else {
          this.is_four = false
        }
        this.is_three = false
        this.getList({ filter: { type: name }, page: this.paging.current })
      } else {
        this.is_three = true
        gzList({ filter: { type: name }, page: this.paging.current }).then((r) => {
          this.data.push(...r.data.data)
          this.loading = false
          this.paging.current = r.data.meta.current_page
          this.paging.total = r.data.meta.total
          this.paging.last_page = r.data.meta.last_page

          if (this.paging.current >= r.data.meta.last_page) {
            this.finished = true
          }
        })
      }
    },
    /* eslint-disable */
    goToDetail(item) {
      switch (this.active) {
        case 1:
          this.$router.push({ name: 'DomesticDetail', params: { id: item.id } })
          break
        case 2:
          this.$router.push({ name: 'IntlDetail', params: { id: item.id } })
          break
        case 3:
          this.$router.push({ name: 'LbtDetail', params: { id: item.id } })
          break
        case 4:
          this.$router.push({ name: 'OtherDetail', params: { id: item.id } })
          break
        case 5:
          this.$router.push({ name: 'GroupDetail', params: { id: item.policy_group_id } })
          break
        case 6:
          this.$router.push({ name: 'OfflineDetail', params: { id: item.id } })
          break
      }
    },
    /* eslint-enable */
    // 下载保单
    downloadFile(item) {
      if (item.type === 6) {
        this.$download(item.policy_file)
        return
      }
      this.$download(downloadFrom(item.id))
    },
    gotoPage(item) {
      const dst = {
        1: 'InsureDomestic',
        2: 'InsureInternational',
        3: 'InsureLbt'
      }

      this.$router.push({ name: dst[item.type], query: { id: item.id, from: 'copy' } })
    }
  }
}
</script>

<style lang="less" scoped>
.manage {
  padding: 0 0 60px;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #fafafa;

  /deep/ .van-sticky {
    background-color: #fff;
    box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05) !important;
    .search-item {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      margin-bottom: 15px;
      .item {
        box-sizing: border-box;
        text-align: center;
        height: 40px;
        line-height: 80px;
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
      .active {
        color: var(--primary-color);
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
  .wrapper {
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    p {
      margin: 0;
      margin: 16px 0;
      display: flex;
      justify-content: space-between;
      .total {
        color: #999;
        font-size: 14px;
      }
      .dropdown {
        color: #333;
        font-size: 14px;
        position: relative;
        margin-right: 16px;
        /deep/ .van-icon {
          position: absolute;
          top: 1px;
          // transform: rotate(90deg);
        }
      }
    }
    .card {
      box-shadow: 4px 4px 20px 0px rgba(0, 0, 0, 0.05);
      border-radius: 20px;
    }
  }
}

.van-cell-group {
  margin-top: 16px;
}
// 箭头方向
.arrowStatus {
  transform: rotate(-90deg);
  padding: 0 6px;
  line-height: 12px;
  height: 12px;
}
// 多余线
.van-cell-group .van-cell:not(:first-child):after {
  border-bottom: none !important;
}
.clickBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  .van-button {
    margin-right: 14px;
    margin-bottom: 16px;
    width: 85px;
    height: 30px;
    font-size: 14px;
    white-space: nowrap;
  }
}
.status-style {
  color: #07c160;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #07c160;
    margin-right: 6px;
  }
}
.drogdown {
  display: block;
}
//
/deep/ .van-tabs__wrap {
  overflow: visible;
  .van-tab {
    padding: 0 7px;
  }
}
// 已退回、未提交
.color1 {
  color: #d0021b;
  &::before {
    background-color: #d0021b;
  }
}
//已审核、已出单、已支付
.color2 {
  color: #5cc374;
  &::before {
    background-color: #5cc374;
  }
}
//已作废、已退保
.color3 {
  color: #979797;
  &::before {
    background-color: #979797;
  }
}
//审核中、退保中、批改中
.color4 {
  color: #fb7203;
  &::before {
    background-color: #fb7203;
  }
}
//已提交、待确认、待支付、暂存单
.color5 {
  color: #f29925;
  &::before {
    background-color: #f29925;
  }
}

.van-mini-space {
  padding: 5px 15px !important;
}
</style>
