<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="保单详情" left-arrow @click-left="$router.push({ name: 'Policies', query: { active: 5 } })" />
    </van-sticky>
    <div class="top-box">
      <van-cell>
        <template #title>
          <div class="cell-left" v-if="form.product">
            <div>{{ form.product.name }}</div>
            <div>{{ form.order_no }}</div>
          </div>
        </template>
        <template #right-icon>
          <!-- <span class="status green-title" v-if="false">已出单</span>
          <span class="status yellow-title">已提交</span> -->
          <!-- <span class="status yellow-title">{{ form.status_text }}</span> -->
          <!-- 已退回、未提交 -->
          <span class="status-style color1" v-if="form.policy.status === 0 || form.policy.status === 10">
            {{ form.status_text }}
          </span>
          <!-- 已审核、已出单、已支付 -->
          <span
            class="status-style color2"
            v-if="form.policy.status === 3 || form.policy.status === 4 || form.status_text === 5"
          >
            {{ form.status_text }}
          </span>
          <!-- 已作废、退保 -->
          <span class="status-style color3" v-if="form.policy.status === 6 || form.policy.status === 7">
            {{ form.status_text }}
          </span>
          <!-- 审核中、退保中、批改中 -->
          <span
            class="status-style color4"
            v-if="form.policy.status === 2 || form.policy.status === 8 || form.status_text === 9"
          >
            {{ form.status_text }}
          </span>
          <!-- 已提交、待确认、待支付、暂存单 -->
          <span
            class="status-style color5"
            v-if="form.policy.status === 1 || form.policy.status === 11 || form.status_text === 12"
            >{{ form.status_text }}</span
          >
        </template>
      </van-cell>
      <div>
        <van-cell class="before-none" title="投保套餐" :value="form.plan.title" :border="false" />
        <van-cell title="投保单位" :value="form.policy.policyholder" :border="false" />
        <van-cell title="投保单位组织机构代" :value="`${form.policy.policyholder_idcard_no}`" :border="false" />
        <van-cell title="在保人数" :value="`${form.insured_employee_count}`" :border="false" />
      </div>
    </div>
    <div class="flex-box">
      <ul class="box-content">
        <li class="item" v-if="[0, 10].includes(form.policy.status)" @click="confirmEditPolicy">
          <div class="wrapper">
            <div class="img-box yellow">
              <img src="@/assets/imgs/manage/edit.png" alt="" />
            </div>
            <div class="text-box">修改保单</div>
          </div>
        </li>
        <li class="item" @click="$router.push({ name: 'GroupPeopleList', params: { id: $route.params.id } })">
          <div class="wrapper">
            <div class="img-box yellow">
              <img src="@/assets/imgs/manage/people.png" alt="" />
            </div>
            <div class="text-box">{{ form.policy.status === 0 ? '继续投保' : '人员列表' }}</div>
          </div>
        </li>
        <li class="item" @click="$router.push({ name: 'GroupEndorses', params: { id: $route.params.id } })">
          <div class="wrapper">
            <div class="img-box" style="background-color: #ff7f4b">
              <img src="@/assets/imgs/manage/endorse.png" alt="" />
            </div>
            <div class="text-box">批单管理</div>
          </div>
        </li>
        <li class="item" @click="$router.push({ name: 'GroupPaymentManagement', params: { id: $route.params.id } })">
          <div class="wrapper">
            <div class="img-box green">
              <img src="@/assets/imgs/manage/pay.png" alt="" />
            </div>
            <div class="text-box">支付记录</div>
          </div>
        </li>
        <li class="item" v-if="form.policy.status === 0" @click="confirmInvalidPolicy">
          <div class="wrapper">
            <div class="img-box red">
              <img src="@/assets/imgs/manage/destroy.png" alt="" />
            </div>
            <div class="text-box">作废</div>
          </div>
        </li>
      </ul>
    </div>
    <div class="item-wrapper" v-if="hasSentBack">
      <van-cell>
        <template #title>
          <span class="word">退回原因</span>
        </template>
      </van-cell>
      <van-notice-bar :text="form.policy.sendback_reason" />
    </div>
    <template
      v-if="productPlatform === 'API_GROUP_ZY' && unpaidPayments.length > 0 && [4, 8].includes(form.policy.status)"
    >
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">在线支付</span>
          </template>
        </van-cell>
        <div class="payment-notice">
          <p class="__pn_title">当前保单为见费出单你需要完成在线支付后方可生效, 如您已完成支付请稍后刷新获取最新信息</p>
          <p class="__pn_amount">支付金额 ¥{{ unpaidPayment.amount }}元</p>
          <van-form @submit="handlePay">
            <template v-if="unpaidPayment.method === -1">
              <van-field
                name="method"
                label="支付方式"
                placeholder="选择支付方式"
                :rules="[{ required: true, type: 'email', message: '请选择支付方式' }]"
              >
                <template #input>
                  <van-radio-group v-model="payment.method">
                    <van-radio :name="1">中意公对公支付</van-radio>
                    <van-radio :name="2">中意微信支付</van-radio>
                    <van-radio :name="3">中意支付宝支付</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
            </template>
            <van-field
              v-model="payment.email"
              name="email"
              label="邮箱"
              placeholder="请输入邮箱地址"
              :rules="[{ required: true, type: 'email', message: '请输入邮箱地址' }]"
            />

            <div style="margin: 16px">
              <van-button plain icon="bill" type="primary" native-type="submit"> 立即支付 </van-button>
            </div>
          </van-form>
        </div>
      </div>
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">投保人信息</span>
          </template>
        </van-cell>
        <van-cell title="企业法人" :value="form.contact_name" />
        <van-cell title="手机号" :value="form.contact_phone" />
        <van-cell title="证件类型" :value="this.certTypes[form.extra_info.policyholder_idcard_type]" />
        <van-cell title="证件号" :value="form.policy.policyholder_idcard_no" />
      </div>
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">被保联系人</span>
          </template>
        </van-cell>
        <van-cell title="企业名称" :value="form.policy.insured" />
        <van-cell title="联系电话" :value="form.policy.insured_phone_number" />
        <van-cell title="证件类型" :value="this.certTypes[form.extra_info.insured_idcard_type]" />
        <van-cell title="证件号" :value="form.policy.insured_idcard_no" />
      </div>
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">投保信息</span>
          </template>
        </van-cell>
        <van-cell title="出单公司" :value="form.policy.company_branch.name" />
        <van-cell title="保单号" :value="form.policy.policy_no" />
        <van-cell title="投保产品" :value="form.product.name" />
        <van-cell title="投保套餐" :value="form.plan.title" />
        <van-cell title="在保人数" :value="form.insured_employee_count" />
        <van-cell title="保费" :value="form.policy.user_premium" />
        <van-cell title="更新时间" :value="form.policy.updated_at" />
        <van-cell title="投保时间" :value="form.policy.created_at" />
        <van-cell title="起保日期" :value="form.start_at" />
        <van-cell title="终保日期" :value="form.end_at" />
      </div>
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">标的信息</span>
          </template>
        </van-cell>
        <van-cell title="省份" :value="form.extra_info.object_address[0]" />
        <van-cell title="市区" :value="form.extra_info.object_address[1]" />
        <van-cell title="详细地址" :value="form.extra_info.object_address_detail" />
      </div>
    </template>
    <template v-else>
      <div class="item-wrapper">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">投保联系人</span>
          </template>
        </van-cell>
        <van-cell title="姓名" :value="form.contact_name" />
        <van-cell title="联系电话" :value="form.contact_phone" />
      </div>
      <div class="item-wrapper" v-if="form.product">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">投保信息</span>
          </template>
        </van-cell>
        <van-cell title="出单公司" :value="form.policy.company_branch.name" />
        <van-cell title="流水号" :value="form.policy.order_no" />
        <van-cell title="保单号" :value="form.policy.policy_no" />
        <van-cell title="投保产品" :value="form.product.name" />
        <van-cell title="投保套餐" :value="form.plan.title" />
        <van-cell title="投保单位" :value="form.policy.policyholder" />
        <van-cell title="投保单位组织机构代码" :value="form.policy.policyholder_idcard_no" />
        <van-cell title="被保单位" :value="form.policy.insured" />
        <van-cell title="被保单位组织机构代码" :value="form.policy.insured_idcard_no" />
        <van-cell title="在保人数" :value="form.insured_employee_count" />
        <van-cell title="保费" :value="form.policy.premium" />
        <van-cell title="更新时间" :value="form.policy.updated_at" />
        <van-cell title="投保时间" :value="form.policy.created_at" />
        <van-cell title="起保时间" :value="form.start_at" />
        <van-cell title="终保时间" :value="form.end_at" />
      </div>
      <div class="item-wrapper" v-if="form.attachment">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">文件信息</span>
          </template>
        </van-cell>
        <van-cell title="被保单位营业执照">
          <template #right-icon>
            <a style="`color: ${primaryColor}`" :href="form.attachment.business_license_file" target="_blank">
              点击查看
            </a>
          </template>
        </van-cell>
        <van-cell title="委托书">
          <template #right-icon>
            <a style="`color: ${primaryColor}`" :href="form.attachment.authorization_file" target="_blank">
              点击查看
            </a>
          </template>
        </van-cell>
        <van-cell title="人员清单">
          <template #right-icon>
            <a style="`color: ${primaryColor}`" :href="form.attachment.staff_list_file" target="_blank">点击查看</a>
          </template>
        </van-cell>
        <van-cell title="人员清单盖章文件">
          <template #right-icon>
            <a style="`color: ${primaryColor}`" :href="form.attachment.staff_stamp_list_file" target="_blank">
              点击查看
            </a>
          </template>
        </van-cell>
        <van-cell title="投保单">
          <template #right-icon>
            <a style="`color: ${primaryColor}`" :href="form.attachment.application_file" target="_blank">点击查看</a>
          </template>
        </van-cell>
        <van-cell title="投保单盖章文件">
          <template #right-icon>
            <a style="`color: ${primaryColor}`" :href="form.attachment.application_stamp_file" target="_blank">
              点击查看
            </a>
          </template>
        </van-cell>
        <van-cell title="其他文件">
          <template #right-icon>
            <a style="`color: ${primaryColor}`" :href="form.attachment.extra_file" target="_blank">点击查看</a>
          </template>
        </van-cell>
      </div>
      <div class="item-wrapper" v-if="form.invoice_info">
        <van-cell>
          <template #title>
            <img src="@/assets/imgs/insure/book.png" alt="" />
            <span class="word">发票信息</span>
          </template>
        </van-cell>
        <van-cell title="发票类型" :value="type[form.invoice_info.type]" />
        <div v-if="form.invoice_info.type !== 'none'">
          <van-cell title="发票抬头" :value="form.invoice_info.title" />
          <van-cell title="纳税人识别号" :value="form.invoice_info.tax_no" />
          <van-cell title="开户行" :value="form.invoice_info.bank_name" />
          <van-cell title="银行卡号" :value="form.invoice_info.bankcard_no" />
          <van-cell title="联系电话" :value="form.invoice_info.phone_number" />
          <van-cell title="注册地址" :value="form.invoice_info.registered_addr" />
        </div>
      </div>
      <!-- 退保弹窗 -->
      <van-action-sheet v-model="show" title="退保原因">
        <div class="content-reason">
          <van-field
            v-model="reason"
            rows="2"
            autosize
            label="退保原因"
            type="textarea"
            maxlength="50"
            placeholder="请输入退保原因"
            show-word-limit
          />
          <div class="reason-box">
            <van-button type="primary" block :color="primaryColor" @click="returnReason">确认退保</van-button>
          </div>
        </div>
      </van-action-sheet>
    </template>
  </div>
</template>

<script>
import { returnReason, downloadFrom, gzDetail, destroyDraft, unpaidPayments, getUnpaidPayment } from '@/apis/manage'

export default {
  data() {
    return {
      type: {
        none: '无需开票',
        normal: '普通发票',
        special: '增值税专用发票'
      },
      certTypes: {
        '01': '身份证',
        '02': '军官证',
        '03': '学生证',
        '04': '台胞证',
        '06': '护照',
        '07': '港澳返乡证',
        '08': '出生证明（未成年人）',
        '09': '营业执照',
        10: '工商登记号',
        11: '组织机构代码',
        13: '统一社会信用代码',
        14: '港澳台居民居住证',
        99: '其他'
      },
      id: this.$route.params.id,
      /* type: 0 为国内货运险
       * type: 1 国际货运险
       * type: 2 单车责任险
       * type: 3 雇主责任险
       * type: 4 其他险种
       */
      form: {
        product: null,
        policy_no: '',
        status_text: '',
        plan: {
          title: ''
        },
        policy: {
          policyholder: '',
          policyholder_phone_number: ''
        }
      },
      contrBtn: false,
      fileName: '',
      inputList: [],
      show: false,
      reason: '',
      unpaidPayments: [],
      payment: {
        method: -1,
        email: ''
      }
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    },
    productPlatform() {
      return this.form.product?.additional?.third_platform
    },
    hasSentBack() {
      return [10].includes(this.form.policy.status)
    },
    unpaidPayment() {
      return this.unpaidPayments?.[0] || {}
    }
  },
  mounted() {
    this.getPageData()
  },
  methods: {
    handlePay() {
      getUnpaidPayment(this.form.policy_id, this.unpaidPayment.id, {
        method: this.payment.method,
        extra: {
          email: this.payment.email
        }
      }).then((r) => {
        window.location.href = r.data.data
      })
    },
    getPageData() {
      gzDetail(this.id).then((r) => {
        this.form = r.data.data

        unpaidPayments(r.data.data.policy_id).then((res) => {
          this.unpaidPayments = res.data.data
        })
      })
    },
    // 退保原因
    returnReason() {
      returnReason(this.id, { reason: this.reason }).then((r) => {
        this.$toast('退保申请成功')
        this.show = false
      })
    },
    // 下载保单
    downloadFile() {
      this.$download(downloadFrom(item.id))
    },
    confirmInvalidPolicy() {
      this.$dialog
        .confirm({
          title: '确认该暂存单作废吗？',
          message: ''
        })
        .then(() => {
          destroyDraft(this.form.policy_id).then(() => {
            this.$toast.success('作废成功')

            this.$router.go(-1)
          })
        })
    },
    confirmEditPolicy() {
      this.$dialog
        .confirm({
          title: '确认修改投保信息吗吗？',
          message: ''
        })
        .then(() => {
          let productType = this.form.product.additional.third_platform
          window.sessionStorage.setItem(
            'gz-write',
            JSON.stringify({
              product_id: this.form.product.id,
              group_plan_id: this.form.plan.id
            })
          )

          if (productType === 'API_GROUP_ZY') {
            this.$router.push({
              name: 'InsureGroupZhongyi',
              query: {
                product_id: this.form.product.id,
                policy_group_id: this.form.id
              }
            })
          } else {
            this.$router.push({
              name: 'InsureGroupBasic',
              query: {
                product_id: this.form.product.id,
                policy_group_id: this.form.id
              }
            })
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }

  .payment-notice {
    padding: 10px 16px 10px 16px;
    font-size: 14px;

    .__pn_title {
      font-size: 14px;
      line-height: 28px;
    }

    .__pn_amount {
      font-size: 18px;
      font-weight: 400;
    }
  }
  .top-box {
    margin: 15px 0 34px;
    padding-bottom: 80px;
    background-color: #fff;
    .van-cell {
      background-color: #ffffff;
      // &:not(&:first-child) {
      padding-bottom: 0;
      // }
      &:first-child:before {
        content: '';
        display: inline-block;
        width: 4px;
        background-color: red;
        margin-right: 8px;
      }
      .cell-left {
        div:first-child {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
        div:last-child {
          color: #333;
          font-size: 16px;
          font-weight: 400;
        }
      }
      .status {
        font-size: 14px;
        font-weight: bold;
      }
      .green-title {
        color: #5cc374;
      }
      .yellow-title {
        color: #f29925;
      }
    }
  }
  .flex-box {
    position: absolute;
    top: 240px;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 0 15px;
    .box-content {
      margin-top: 20px;
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      .item {
        flex: 1;
        padding: 15px 0;
        text-align: center;
        .wrapper {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: center;
          height: 50px;
          .img-box {
            height: 34px;
            width: 34px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 16px;
              // height: 16px;
              background-size: contain;
            }
          }

          .text-box {
            color: #333;
            font-size: 12px;
          }
        }
      }
    }
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
    img {
      width: 12px;
      margin-right: 5px;
      position: relative;
      top: 2px;
    }
    .word {
      font-size: 16px;
      font-weight: bold;
    }
    .submit-btn {
      padding: 15px;
    }
  }
}
// 文件信息

.file-info {
  /deep/ .van-cell__title {
    color: #999;
    font-size: 14px;
  }
  .van-cell {
    & > span {
      color: var(--primary-color);
      font-size: 14px;
    }
  }
}
// 背景色
.yellow {
  background-color: #ffb85b;
}
.red {
  background-color: #ff5656;
}
.green {
  background-color: #7cc865;
}
.blue {
  background-color: #6f94ff;
}
// 已退回、未提交
.color1 {
  color: #d0021b;
}
//已审核、已出单、已支付
.color2 {
  color: #5cc374;
}
//已作废、已退保
.color3 {
  color: #979797;
}
//审核中、退保中、批改中
.color4 {
  color: #fb7203;
}
//已提交、待确认、待支付、暂存单
.color5 {
  color: #f29925;
}
.van-overlay {
  z-index: 20;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}
.reason-box {
  padding: 15px 15px 30px;
}
/deep/ .before-none {
  &::before {
    content: none !important;
  }
}
</style>
