<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="保单详情" left-arrow @click-left="$router.push({ name: 'Policies', query: { active: 4 } })" />
    </van-sticky>
    <div class="top-box">
      <van-cell>
        <template #title>
          <div class="cell-left">
            <div>{{ form.product?.name }}</div>
            <div>{{ form.order_no }}</div>
          </div>
        </template>
        <template #right-icon>
          <!-- <span class="status green-title" v-if="false">已出单</span>
          <span class="status yellow-title">已提交</span> -->
          <!-- <span class="status yellow-title">{{ form.status_text }}</span> -->
          <!-- 已退回、未提交 -->
          <span class="status-style color1" v-if="form.status === 0 || form.status === 10">
            {{ form.status_text }}
          </span>
          <!-- 已审核、已出单、已支付 -->
          <span class="status-style color2" v-if="form.status === 3 || form.status === 4 || form.status === 5">
            {{ form.status_text }}
          </span>
          <!-- 已作废、退保 -->
          <span class="status-style color3" v-if="form.status === 6 || form.status === 7">{{ form.status_text }}</span>
          <!-- 审核中、退保中、批改中 -->
          <span class="status-style color4" v-if="form.status === 2 || form.status === 8 || form.status === 9">
            {{ form.status_text }}
          </span>
          <!-- 已提交、待确认、待支付、暂存单 -->
          <span class="status-style color5" v-if="form.status === 1 || form.status === 11 || form.status === 12">
            {{ form.status_text }}
          </span>
        </template>
      </van-cell>
      <div>
        <van-cell class="before-none" title="意外损伤" :value="form.product?.main_clause" :border="false" />
        <van-cell title="住院津贴" :value="form.product?.additional_clauses" :border="false" />
        <van-cell title="意外医疗" :value="`￥${form.coverage}`" :border="false" />
      </div>
    </div>
    <div class="flex-box">
      <ul class="box-content">
        <template v-if="loggedId === data?.user?.id">
          <li
            class="item"
            v-if="form.status === 5"
            @click="
              $router.push({
                name: 'InsureOtherForm',
                params: { id: form.product?.id },
                query: { from: 'edit', policy_id: id }
              })
            "
          >
            <div class="wrapper">
              <div class="img-box yellow">
                <img src="@/assets/imgs/manage/edit.png" alt="" />
              </div>
              <div class="text-box">编辑</div>
            </div>
          </li>
          <template v-if="form.status === 0">
            <li
              class="item"
              @click="
                $router.push({
                  name: 'InsureOtherForm',
                  params: { id: form.product?.id },
                  query: { from: 'continue', policy_id: id }
                })
              "
            >
              <div class="wrapper">
                <div class="img-box skyblue">
                  <img src="@/assets/imgs/manage/continue.png" alt="" />
                </div>
                <div class="text-box">继续投保</div>
              </div>
            </li>
            <li class="item" @click="confirmDestroy">
              <div class="wrapper">
                <div class="img-box red">
                  <img src="@/assets/imgs/manage/destroy.png" alt="" />
                </div>
                <div class="text-box">作废</div>
              </div>
            </li>
          </template>
          <li class="item" v-if="form.status === 5" @click="surrender.actionSheet = true">
            <div class="wrapper">
              <div class="img-box red">
                <img src="@/assets/imgs/manage/bank.png" alt="" />
              </div>
              <div class="text-box">退保</div>
            </div>
          </li>
          <li class="item" @click="$router.push({ name: 'OtherPaymentHistory', params: { id: id } })">
            <div class="wrapper">
              <div class="img-box green">
                <img src="@/assets/imgs/manage/fy.png" alt="" />
              </div>
              <div class="text-box">支付记录</div>
            </div>
          </li>
        </template>
        <li class="item" @click="downloadFile">
          <div class="wrapper">
            <div class="img-box blue">
              <img src="@/assets/imgs/manage/write.png" alt="" />
            </div>
            <div class="text-box">电子保单</div>
          </div>
        </li>
      </ul>
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">投保联系人</span>
        </template>
      </van-cell>
      <van-cell title="姓名" :value="form.detail?.applicant_contact_name" />
      <van-cell title="电话" :value="form.detail?.applicant_contact_phone" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">投保信息</span>
        </template>
      </van-cell>
      <van-cell title="表单号" :value="form.policy_no" />
      <van-cell title="流水号" :value="form.order_no" />
      <van-cell title="投保人" :value="form.policyholder" />
      <van-cell title="被保人" :value="form.insured" />
      <van-cell title="起保日期" :value="form.detail?.start_at" />
      <van-cell title="终保日期" :value="form.detail?.end_at" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">投保资料</span>
        </template>
      </van-cell>
      <template>
        <div v-for="(item, index) in customer" :key="index">
          <van-cell v-if="item.type === 'file'" :title="item.title">
            <template #right-icon>
              <a style="`color: ${primaryColor}`" :href="item.value" target="_blank">点击查看</a>
            </template>
          </van-cell>

          <van-cell v-else :title="item.title" :value="item.value" />
        </div>
      </template>
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">发票类型</span>
        </template>
      </van-cell>
      <van-cell title="发票类型" :value="form.detail?.invoice_content?.invoice_type | invoiceType" />
      <template v-if="['plain', 'special'].includes(form.detail?.invoice_content?.invoice_type)">
        <van-cell title="发票抬头" :value="form.detail?.invoice_content?.title" />
        <van-cell title="纳税人识别号" :value="form.detail?.invoice_content?.tax_no" />
      </template>
      <template v-if="form.detail?.invoice_content?.invoice_type === 'special'">
        <van-cell title="开户行" :value="form.detail?.invoice_content?.bank_name" />
        <van-cell title="账号" :value="form.detail?.invoice_content?.bankcard_no" />
        <van-cell title="地址" :value="form.detail?.invoice_content?.registered_addr" />
        <van-cell title="电话" :value="form.detail?.invoice_content?.registered_phone_number" />
      </template>
    </div>
    <!-- 退保弹窗 -->
    <van-action-sheet v-model="surrender.actionSheet" title="退保原因">
      <div class="content-reason">
        <van-field
          v-model="surrender.reason"
          rows="2"
          autosize
          label="退保原因"
          type="textarea"
          maxlength="50"
          placeholder="请输入退保原因"
          show-word-limit
        />
        <div class="reason-box">
          <van-button type="primary" block :color="primaryColor" @click="confirmSurrender">确认退保</van-button>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { manageDetail, surrender, downloadFrom, destroyDraft } from '@/apis/manage'
import { userKey } from '@/config'

export default {
  data() {
    return {
      customer: [],
      id: this.$route.params.id,
      form: {},
      surrender: {
        actionSheet: false,
        reason: ''
      }
    }
  },
  filters: {
    invoiceType(type) {
      const types = {
        none: '无需发票',
        plain: '普票',
        special: '专票'
      }

      return types[type]
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    },
    loggedId() {
      const user = JSON.parse(localStorage.getItem(userKey) || '{}')

      return user?.id
    }
  },
  mounted() {
    this.getPageData()
  },
  methods: {
    getPageData() {
      manageDetail(this.id).then((r) => {
        this.form = r.data.data
        this.customer = JSON.parse(r.data.data.detail.addition)
        this.form.detail.invoice_content = JSON.parse(this.form.detail.invoice_content)
      })
    },
    // 退保原因
    confirmSurrender() {
      surrender(this.id, { reason: this.surrender.reason }).then((r) => {
        this.$toast.success('退保申请成功')

        this.surrender.actionSheet = false
      })
    },
    // 下载保单
    downloadFile() {
      this.$download(downloadFrom(this.id))
    },
    confirmDestroy() {
      this.$dialog
        .confirm({
          title: 'Title',
          message: 'Content'
        })
        .then(() => {
          destroyDraft(this.id).then(() => {
            this.$toast.success('作废成功')

            this.$router.go(-1)
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .top-box {
    margin: 15px 0 34px;
    padding-bottom: 80px;
    background-color: #fff;
    .van-cell {
      background-color: #ffffff;
      // &:not(&:first-child) {
      padding-bottom: 0;
      // }
      &:first-child:before {
        content: '';
        display: inline-block;
        width: 4px;
        background-color: red;
        margin-right: 8px;
      }
      .cell-left {
        div:first-child {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
        div:last-child {
          color: #333;
          font-size: 16px;
          font-weight: 400;
        }
      }
      .status {
        font-size: 14px;
        font-weight: bold;
      }
      .green-title {
        color: #5cc374;
      }
      .yellow-title {
        color: #f29925;
      }
    }
  }
  .flex-box {
    position: absolute;
    top: 240px;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 0 15px;
    .box-content {
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      .item {
        flex: 1;
        padding: 15px 0;
        text-align: center;
        .wrapper {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: center;
          height: 50px;
          .img-box {
            height: 34px;
            width: 34px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 16px;
              // height: 16px;
              background-size: contain;
            }
          }

          .text-box {
            color: #333;
            font-size: 12px;
          }
        }
      }
    }
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
    img {
      width: 12px;
      margin-right: 5px;
      position: relative;
      top: 2px;
    }
    .word {
      font-size: 16px;
      font-weight: bold;
    }
    .submit-btn {
      padding: 15px;
    }
  }
}
// 文件信息

.file-info {
  /deep/ .van-cell__title {
    color: #999;
    font-size: 14px;
  }
  .van-cell {
    & > span {
      color: var(--primary-color);
      font-size: 14px;
    }
  }
}
// 背景色
.yellow {
  background-color: #ffb85b;
}
.red {
  background-color: #ff5656;
}
.green {
  background-color: #7cc865;
}
.blue {
  background-color: #6f94ff;
}
// 已退回、未提交
.color1 {
  color: #d0021b;
}
//已审核、已出单、已支付
.color2 {
  color: #5cc374;
}
//已作废、已退保
.color3 {
  color: #979797;
}
//审核中、退保中、批改中
.color4 {
  color: #fb7203;
}
//已提交、待确认、待支付、暂存单
.color5 {
  color: #f29925;
}

.skyblue {
  background-color: #21cae3;
}

.van-overlay {
  z-index: 20;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}
.reason-box {
  padding: 15px 15px 30px;
}
/deep/ .before-none {
  &::before {
    content: none !important;
  }
}
</style>
