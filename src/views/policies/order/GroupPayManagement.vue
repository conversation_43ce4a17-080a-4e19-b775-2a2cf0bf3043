<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="支付记录" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <ul class="peopleList">
      <li class="peopleList-item" v-for="(item, index) in list" :key="index">
        <div class="wrapper">
          <van-cell>
            <template #title
              ><span class="title">￥{{ item.amount }}</span></template
            >
            <template #right-icon>
              <span class="replace" :class="item.status === 1 ? 'green' : 'yellow'">
                {{ status[item.status - 1].name }}
              </span>
            </template>
          </van-cell>
          <van-field label="支付流水号" :placeholder="item.transaction_no" disabled :border="false" />
          <van-field label="支付方式" :placeholder="item.remark" disabled :border="false" />
          <van-field label="充值时间" :placeholder="item.created_at" disabled :border="false" />
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { payhistory } from '@/apis/manage'

export default {
  data() {
    return {
      list: [],
      status: [
        { name: '已支付', value: '1' },
        { name: '已到账', value: '2' },
        { name: '已拒接', value: '3' },
        { name: '已提交', value: '4' }
      ]
    }
  },
  mounted() {
    payhistory(this.$route.params.id).then((r) => {
      this.list = r.data.data
    })
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left,
  /deep/ .van-icon-search {
    color: #333;
  }
  .peopleList {
    padding: 0 15px;
    margin-top: 15px;
    margin-bottom: 15px;
    // padding-bottom: 15px;
    .wrapper {
      border-bottom: 1px solid #f4f4f4;
    }
    .peopleList-item {
      margin-bottom: 15px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      // margin-bottom: 15px;
      .title {
        color: #333333;
        font-size: 14px;
      }
      .replace {
        font-size: 14px;
        &::before {
          content: '';
          display: inline-block;
          width: 6px;
          height: 6px;
          vertical-align: middle;
          background-color: var(--primary-color);
          border-radius: 50%;
          margin-right: 6px;
        }
      }
      .yellow {
        color: var(--primary-color);
        &::before {
          background-color: var(--primary-color);
        }
      }
      .green {
        color: #5cc374;
        &::before {
          background-color: #5cc374;
        }
      }
      .van-field:not(.van-field:last-child) {
        padding-bottom: 0;
      }
    }
  }
}
</style>
