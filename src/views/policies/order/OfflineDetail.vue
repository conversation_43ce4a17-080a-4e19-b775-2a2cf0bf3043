<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="保单详情" left-arrow @click-left="$router.push({ name: 'Policies', query: { active: 1 } })" />
    </van-sticky>
    <div class="top-box">
      <van-cell>
        <template #title>
          <div class="cell-left">
            <div>{{ data.product?.name }}</div>
            <div>{{ data.order_no }}</div>
          </div>
        </template>
        <template #right-icon>
          <!-- <span class="status green-title" v-if="false">已出单</span>
          <span class="status yellow-title">已提交</span> -->
          <!-- <span class="status yellow-title">{{ data.status_text }}</span> -->
          <!-- 已退回、未提交 -->
          <span class="status-style color1" v-if="data.status === 0 || data.status === 10 || data.status === 13">
            {{ data.status_text }}
          </span>
          <!-- 已审核、已出单、已支付 -->
          <span class="status-style color2" v-if="data.status === 3 || data.status === 4 || data.status === 5">
            {{ data.status_text }}
          </span>
          <!-- 已作废、退保 -->
          <span class="status-style color3" v-if="data.status === 6 || data.status === 7">{{ data.status_text }}</span>
          <!-- 审核中、退保中、批改中 -->
          <span class="status-style color4" v-if="data.status === 2 || data.status === 8 || data.status === 9">
            {{ data.status_text }}
          </span>
          <!-- 已提交、待确认、待支付、暂存单 -->
          <span class="status-style color5" v-if="data.status === 1 || data.status === 11 || data.status === 12">
            {{ data.status_text }}
          </span>
        </template>
      </van-cell>
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">系统信息</span>
        </template>
      </van-cell>
      <van-cell title="保单号" :value="data.policy_no" />
      <van-cell title="出单时间" :value="data.issued_at" />
      <van-cell title="流水号" :value="data.order_no" />
      <van-cell title="投保用户" :value="data.user?.name" />
      <van-cell title="投保时间" :value="data.created_at" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">产品信息</span>
        </template>
      </van-cell>
      <van-cell title="险类" :value="data.detail.category_name" />
      <van-cell title="险种" :value="data.detail.insurance_name" />
      <van-cell title="保险公司" :value="data.company?.name" />
      <van-cell title="保险产品" :value="data.product?.name" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">基本信息</span>
        </template>
      </van-cell>
      <van-cell title="投保人" :value="data.policyholder" />
      <van-cell title="被保人" :value="data.insured" />
      <van-cell title="保单起始日期" :value="data.detail.start_at" />
      <van-cell title="保单终止日期" :value="data.detail.end_at" />
      <van-cell title="业务员" :value="data.salesman.name" />
      <van-cell title="结算方式" :value="data.detail?.settlement_type == 1 ? '含税保费' : '不含税保费'" />
      <van-cell title="业务类型" :value="data.detail?.business_type == 1 ? '会员业务' : '代理业务'" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">费用信息</span>
        </template>
      </van-cell>
      <van-cell title="保费(元)" :value="data.premium" />
    </div>
    <div class="item-wrapper none-r">
      <div class="btn-box">
        <van-button block :color="primaryColor" size="small" @click="downloadFile()">下载保单</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { manageDetail, surrender, downloadFrom, destroyDraft } from '@/apis/manage'
import { userKey } from '@/config'

export default {
  data() {
    return {
      id: this.$route.params.id,
      data: {},
      surrender: {
        actionSheet: false,
        reason: ''
      }
    }
  },
  computed: {
    loggedId() {
      const user = JSON.parse(localStorage.getItem(userKey) || '{}')

      return user?.id
    },
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  created() {
    this.getPageData()
  },
  methods: {
    getPageData() {
      manageDetail(this.id).then((r) => {
        this.data = r.data.data
      })
    },
    // 退保原因
    confirmSurrender() {
      surrender(this.id, { reason: this.surrender.reason }).then((r) => {
        this.$toast.success('退保申请成功')

        this.getPageData()

        this.surrender.actionSheet = false
      })
    },
    confirmDestroy() {
      destroyDraft(this.id).then(() => {
        this.$toast.success('作废成功')

        this.$router.go(-1)
      })
    },
    // 下载保单
    downloadFile() {
      this.$download(this.data.policy_file)
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .top-box {
    margin: 15px 0 15px;
    padding-bottom: 10px;
    background-color: #fff;
    .van-cell {
      background-color: #ffffff;
      // &:not(&:first-child) {
      padding-bottom: 0;
      // }
      &:first-child:before {
        content: '';
        display: inline-block;
        width: 4px;
        background-color: red;
        margin-right: 8px;
      }
      .cell-left {
        div:first-child {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
        div:last-child {
          color: #333;
          font-size: 16px;
          font-weight: 400;
        }
      }
      .status {
        font-size: 14px;
        font-weight: bold;
      }
      .green-title {
        color: #5cc374;
      }
      .yellow-title {
        color: #f29925;
      }
    }
  }
  .flex-box {
    padding: 0 15px;
    .box-content {
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      .item {
        flex: 1;
        padding: 15px 0;
        text-align: center;
        .wrapper {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: center;
          height: 50px;
          .img-box {
            height: 34px;
            width: 34px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 16px;
              // height: 16px;
              background-size: contain;
            }
          }

          .text-box {
            color: #333;
            font-size: 12px;
          }
        }
      }
    }
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
    img {
      width: 12px;
      margin-right: 5px;
      position: relative;
      top: 2px;
    }
    .word {
      font-size: 16px;
      font-weight: bold;
    }
    .submit-btn {
      padding: 15px;
    }
  }
}

.btn-box {
  padding: 10px 15px;
  .van-button {
    margin-bottom: 10px;
  }
}
// 文件信息

.file-info {
  /deep/ .van-cell__title {
    color: #999;
    font-size: 14px;
  }
  .van-cell {
    & > span {
      color: var(--primary-color);
      font-size: 14px;
    }
  }
}
// 背景色
.yellow {
  background-color: #ffb85b;
}
.red {
  background-color: #ff5656;
}
.green {
  background-color: #7cc865;
}
.blue {
  background-color: #6f94ff;
}
.skyblue {
  background-color: #21cae3;
}
// 已退回、未提交
.color1 {
  color: #d0021b;
}
//已审核、已出单、已支付
.color2 {
  color: #5cc374;
}
//已作废、已退保
.color3 {
  color: #979797;
}
//审核中、退保中、批改中
.color4 {
  color: #fb7203;
}
//已提交、待确认、待支付、暂存单
.color5 {
  color: #f29925;
}
.van-overlay {
  z-index: 20;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}
.reason-box {
  padding: 15px 15px 30px;
}
/deep/ .before-none {
  &::before {
    content: none !important;
  }
}
</style>
