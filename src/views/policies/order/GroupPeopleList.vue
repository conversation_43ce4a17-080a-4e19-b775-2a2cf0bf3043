<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar
        title="人员列表"
        left-arrow
        @click-left="$router.push({ name: 'GroupDetail', params: { id: $route.params.id } })"
      />
    </van-sticky>
    <div class="sub-title">
      <span>在保人数：{{ policy.insured_employee_count }}人</span>
    </div>
    <!-- 人员列表 -->
    <ul class="peopleList">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多啦" @load="onLoad">
        <li class="peopleList-item" v-for="item in peopleList" :key="item.id">
          <div class="wrapper">
            <van-cell>
              <template #title
                ><span class="title">{{ item.name }}({{ item.job.name }})</span></template
              >
              <template #right-icon>
                <span class="replace" :class="{ green: item.status === 4, yellow: item.status !== 4 }">{{
                  statusExplains(item.status)
                }}</span>
              </template>
            </van-cell>
            <van-field label="身份证号" :placeholder="item.idcard_no" disabled :border="false" />
            <van-field label="手机号" :placeholder="item.mobile" disabled :border="false" />
            <van-field label="保费" :placeholder="`¥${item.fee}`" disabled :border="false" />
          </div>
          <ul class="options">
            <li class="options-item" @click="deletePeople(item.id)" v-if="item.status !== 4">
              <div>
                <img src="@/assets/imgs/manage/cx.png" alt="" />
                <span>撤销</span>
              </div>
            </li>
            <li
              v-if="item.status === 4"
              class="options-item"
              @click="
                pjpeople = true
                pjId = item.id
              "
            >
              <div>
                <img src="@/assets/imgs/manage/js.png" alt="" />
                <span>批减</span>
              </div>
            </li>
            <li class="options-item" @click="gotoReplace(item.id)" v-if="item.status === 4">
              <div>
                <img src="@/assets/imgs/manage/th.png" alt="" />
                <span>替换</span>
              </div>
            </li>
          </ul>
        </li>
      </van-list>
    </ul>

    <!-- 吸底 -->
    <ul class="bottom-fixed" v-show="editable">
      <li class="bottom-item" @click="handleAddEmployee">
        <div>
          <img src="@/assets/imgs/manage/tj.png" alt="" />
          <span>添加人员</span>
        </div>
      </li>
      <li class="bottom-item" @click="getPay">
        <div>
          <img src="@/assets/imgs/manage/fy.png" alt="" />
          <span>计算费用</span>
        </div>
      </li>
    </ul>
    <!-- 计算费用 -->
    <van-popup
      class="computed-money"
      v-model="show"
      round
      position="bottom"
      :style="{
        height: policy?.product?.additional?.payment_type === 2 && money < 0 ? '60%' : '40%',
        paddingBottom: '10px'
      }"
    >
      <van-form ref="form" @submit="handleSubmit">
        <div class="money-item">
          <img src="@/assets/imgs/manage/payMoney.png" alt="" />
          <span>
            需要{{ money >= 0 ? '支付' : '退回' }}保费 <i>￥{{ money }}</i>
          </span>
        </div>
        <div
          class="money-item"
          style="margin-top: 20px"
          v-if="policy?.product?.additional?.payment_type === 2 && money >= 0"
        >
          <p>该产品为见费出单需等待审核完成后完成支付方可生效</p>
        </div>
        <template v-if="policy?.product?.additional?.payment_type === 1">
          <div class="money-item with-border" v-if="money > 0">
            <van-uploader v-model="fileList" :max-count="1" :max-size="4 * 1024 * 1024">
              <van-icon name="guide-o" />
              <span>上传支付凭证</span>
              <br />
              <i>支持jpg、png、pdf,文件大小不超过4M</i>
            </van-uploader>
          </div>

          <template v-if="policy?.product?.additional?.third_platform !== 'API_GROUP_ZY'">
            <div class="money-item with-border" v-if="[5, 8].includes(policy.status)">
              <van-uploader v-model="endorseFileList" :max-count="1" :max-size="4 * 1024 * 1024">
                <van-icon name="guide-o" />
                <span>上传批单文件</span>
                <br />
                <i>支持jpg、png、pdf,文件大小不超过4M</i>
              </van-uploader>
            </div>
            <div
              style="margin-top: 10px"
              v-if="[5, 8].includes(policy.status) && policy?.product?.additional?.revision_template_file"
            >
              <van-button
                type="primary"
                :color="primaryColor"
                plain
                block
                native-type="submit"
                :url="policy?.product?.additional?.revision_template_file"
              >
                <van-icon name="down" />
                下载批单申请书模板
              </van-button>
            </div>
          </template>
        </template>
        <template v-else>
          <template v-if="money < 0">
            <p style="margin-top: 0.5rem; margin-bottom: 00.5rem">
              {{ policy?.policy?.payment_method !== 1 ? '请输入投(被)保人收款信息' : '请输入原支付账户信息' }}
            </p>
            <van-field
              label="开户名"
              v-model="form.accountName"
              placeholder="请输入开户名"
              :rules="[{ required: true, message: '请输入开户名' }]"
            />
            <van-field
              label="收款账号"
              v-model="form.accountcode"
              placeholder="请输入收款账号"
              :rules="[{ required: true, message: '请输入收款账号' }]"
            />
            <van-field
              readonly
              clickable
              :value="refundBank"
              label="收款银行"
              placeholder="请选择收款银行"
              right-icon="arrow"
              @click="refundBankPicker = true"
              :rules="[{ required: true, message: '请选择收款银行' }]"
            />
            <van-popup v-model="refundBankPicker" position="bottom">
              <van-picker
                show-toolbar
                :columns="bankColumns"
                @confirm="onConfirmBank"
                @cancel="refundBankPicker = false"
              />
            </van-popup>
            <van-field
              readonly
              clickable
              label="开户地址"
              :value="refundBankAddress"
              placeholder="点击选择开户地址"
              @click="refundBankAddressPicker = true"
              right-icon="arrow"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <van-popup v-model="refundBankAddressPicker" position="bottom">
              <van-area
                :area-list="areaList"
                @confirm="onConfirmBankAddress"
                @cancel="refundBankAddressPicker = false"
                :value="refundBankAddress"
                :columns-num="2"
              />
            </van-popup>
            <van-field
              label="开户行"
              v-model="form.customBankName"
              placeholder="请输入开户行"
              :rules="[{ required: true, message: '请输入开户行' }]"
            />
            <van-field
              label="银联行号"
              v-model="form.customBankCode"
              placeholder="请输入银联行号"
              :rules="[{ required: true, message: '请输入银联行号' }]"
            />
            <van-field
              label="付款备注"
              v-model="form.compensateRemark"
              placeholder="请输入付款备注"
              :rules="[{ required: true, message: '请输入付款备注' }]"
            />
          </template>
        </template>
        <van-button style="margin-top: 1rem" type="primary" :color="primaryColor" block native-type="submit">
          确认提交
        </van-button>
      </van-form>
    </van-popup>
    <!-- 批减 -->
    <van-popup class="pj-people" v-model="pjpeople" round position="bottom" :style="{ height: '30%' }">
      <div class="icon"><van-icon name="add-o" /></div>
      <div class="sure">确认批减该人员？</div>
      <div class="btn-box">
        <van-button :color="primaryColor" block @click="surePj">确认批减</van-button>
        <van-button block @click="pjpeople = false">取消</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { peopleList, returnPeople, getPayMoney, submitMoney, newPeopleList, peoplePj, gzDetail } from '@/apis/manage'
import banks from '@/utils/banks'
import { areaList } from '@vant/area-data'

export default {
  data() {
    return {
      // 计算费用
      show: false,
      // 批减
      pjpeople: false,
      money: '',
      fileList: [],
      endorseFileList: [],
      pjId: void 0,
      policy: {},
      peopleList: [],
      loading: false,
      finished: false,
      paging: {
        current: 0,
        total: 0
      },
      banks,
      areaList,
      refundBankPicker: false,
      refundBank: '',
      refundBankAddressPicker: false,
      refundBankAddress: '',
      form: {
        accountName: '',
        accountcode: '',
        bankcode: '',
        bankname: '',
        customBankCode: '',
        customBankName: '',
        bankAddress: '',
        bankProvince: '',
        bankCity: '',
        unionBank: '',
        compensateRemark: '批单退款',
        recareaCode: '',
        reccityName: ''
      }
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    },
    bankColumns() {
      const bankColumns = []
      Object.keys(this.banks).forEach((key) => {
        bankColumns.push({
          value: key,
          text: this.banks[key]
        })
      })

      return bankColumns
    },
    editable() {
      if (this.policy?.policy?.company?.identifier === 'ZYIC') {
        return [0, 5, 10].includes(this.policy?.policy?.status)
      } else {
        return [0, 5, 8, 10].includes(this.policy?.policy?.status)
      }
    }
  },
  watch: {
    'form.accountcode'(value) {
      value = value.replace(' ', '')
      if (value != this.form.accountcode) {
        this.form.accountcode = value
      }
    },
    'form.customBankCode'(value) {
      this.form.unionBank = value
    }
  },
  mounted() {
    this.fetchPolicyDetail()
  },
  methods: {
    onConfirmBank(value) {
      this.form.bankcode = value.value
      this.form.bankname = value.text
      this.refundBank = value.text

      this.refundBankPicker = false
    },
    onConfirmBankAddress(value) {
      const specialCity = ['110000', '310000', '120000', '500000']
      this.form.bankProvince = value[0]['code'].slice(0, 2)

      if (specialCity.includes(value[0]['code'])) {
        this.form.bankCity = value[0]['code'].slice(0, 4)
        this.form.recareaCode = this.form.bankCity
      } else {
        this.form.bankCity = value[1]['code'].slice(0, 4)
        this.form.recareaCode = this.form.bankCity
      }

      this.form.reccityName = value[1].name

      this.refundBankAddress = [value[0].name, value[1].name].join('-')

      this.refundBankAddressPicker = false
    },
    onLoad() {
      this.paging.current++

      this.fetchPeople()
    },
    fetchPolicyDetail() {
      gzDetail(this.$route.params.id).then((r) => {
        this.policy = r.data.data
      })
    },
    handleAddEmployee() {
      if (!this.isOperable([1, 2])) {
        return this.$toast.fail('不允许同时进行批增/批减/替换操作')
      }

      this.$router.push({ name: 'GroupAddPeople', params: { id: this.$route.params.id } })
    },
    // 替换
    gotoReplace(id) {
      if (!this.isOperable([0, 2])) {
        return this.$toast.fail('不允许同时进行批增批减')
      }

      this.$router.push({
        name: 'GroupReplacePeople',
        params: {
          employee_id: id,
          policy_group_id: this.$route.params.id
        }
      })
    },
    // 批减
    surePj() {
      if (!this.isOperable([0, 1])) {
        return this.$toast.fail('不允许同时进行批增或替换')
      }

      const data = {
        policy_group_id: this.$route.params.id,
        action: 'remove',
        employee_id: this.pjId
      }
      peoplePj(data).then(() => {
        this.$toast.success('人员批减成功')
        this.pjpeople = false
        this.peopleList = []
        this.paging = {
          current: 1,
          total: 0
        }

        this.fetchPeople()
      })
    },
    fetchPeople() {
      let employee = []
      peopleList(this.$route.params.id, { per_page: 999 })
        .then((r) => {
          employee = r.data.data
        })
        .then(() => {
          newPeopleList(this.$route.params.id, { page: this.paging.current }).then((r) => {
            if (this.paging.current === 1) {
              employee = employee.concat(r.data.data)
            } else {
              employee = r.data.data
            }

            this.loading = false
            this.paging.current = r.data.meta.current_page
            this.paging.total = r.data.meta.total
            this.paging.last_page = r.data.meta.last_page

            if (this.paging.current >= r.data.meta.last_page) {
              this.finished = true
            }

            this.peopleList.push(...employee)
          })
        })
    },
    // 撤销
    deletePeople(id) {
      returnPeople(id).then((r) => {
        this.$toast('撤销成功')
        this.peopleList = []
        this.paging = {
          current: 1,
          total: 0
        }
        this.fetchPeople()
      })
    },
    isOperable(conflictActions) {
      if (
        (this.policy?.product?.additional?.third_platform === 'API_GROUP_ZY' &&
          this.policy?.product?.additional?.payment_type === 2) ||
        this.policy?.product?.additional?.third_platform === 'PINGAN'
      ) {
        return !this.peopleList.some((e) => conflictActions.includes(e.action) && e.status === 1)
      } else {
        return true
      }
    },
    // 计算保费
    getPay() {
      getPayMoney(this.$route.params.id).then((r) => {
        this.show = true
        this.money = r.data.data.money
      })
    },
    handleSubmit() {
      const file = this.fileList[0]?.file
      let _data = {}
      if (file instanceof File) {
        _data = Object.assign({}, { transaction_file: file })
      }
      const endorseFile = this.endorseFileList[0]?.file
      if (endorseFile instanceof File) {
        _data = Object.assign({}, _data, { endorse_stamp_file: endorseFile })
      }

      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      submitMoney(this.$route.params.id, Object.assign({}, _data, this.form)).then(() => {
        this.show = false
        loading.clear()
        this.$toast.success('提交成功')

        this.$router.push({ name: 'Policies' })
      })
    },
    statusExplains(status) {
      return {
        0: '无效',
        1: '未提交',
        2: '已提交',
        3: '审核中',
        4: '已生效',
        5: '已替换',
        6: '已批减'
      }[status]
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left,
  /deep/ .van-icon-search {
    color: #333;
  }
  .sub-title {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    .sub-left {
      color: #999;
      font-size: 14px;
    }
    .sub-right {
      color: #333333;
      font-size: 14px;
      display: flex;
      align-items: center;
      span {
        margin: 0 5px;
      }
    }
  }
  .options {
    height: 39px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    .options-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      div {
        img {
          width: 13px;
          vertical-align: middle;
          margin-right: 4px;
        }
        span {
          font-size: 14px;
          vertical-align: middle;
          color: var(--primary-color);
        }
      }
    }
  }
  .peopleList {
    padding: 15px;
    padding-bottom: 60px;
    .wrapper {
      border-bottom: 1px solid #f4f4f4;
    }
    .peopleList-item {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      margin-bottom: 15px;
      .title {
        color: #333333;
        font-size: 14px;
      }
      .replace {
        font-size: 14px;
        &::before {
          content: '';
          display: inline-block;
          width: 6px;
          height: 6px;
          vertical-align: middle;
          background-color: var(--primary-color);
          border-radius: 50%;
          margin-right: 6px;
        }
      }
      .yellow {
        color: var(--primary-color);
        &::before {
          background-color: var(--primary-color);
        }
      }
      .green {
        color: #5cc374;
        &::before {
          background-color: #5cc374;
        }
      }
      .van-field:not(.van-field:last-child) {
        padding-bottom: 0;
      }
    }
  }
  .bottom-fixed {
    height: 45px;
    background-color: var(--primary-color);
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    .bottom-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      div {
        img {
          width: 16px;
          vertical-align: middle;
          margin-right: 5px;
        }
        span {
          color: #fff;
          font-size: 17px;
          vertical-align: middle;
        }
      }
    }
    .bottom-item:nth-child(1) {
      border-right: 1px solid #f4f4f4;
    }
  }
  .computed-money {
    display: flex;
    align-items: center;
    flex-direction: column;
    .money-item:first-child {
      margin-top: 64px;
      text-align: center;
      img {
        width: 18px;
      }
      span {
        color: #333;
        font-weight: bold;
        font-size: 18px;
        i {
          font-style: normal;
          color: #ea062b;
        }
      }
    }
    .with-border {
      border: 1px solid var(--primary-color);
      color: #999;
      font-size: 15px;
      border-radius: 4px;
      margin-top: 15px;
      padding: 0.1rem 0.4rem;
      // width: 100%;
      box-sizing: border-box;
      i {
        font-style: normal;
        color: var(--primary-color);
      }
    }
    .money-item:last-child {
      margin-top: 30px;
      box-sizing: border-box;
      width: 100%;
      padding: 0 15px;
    }
  }
  .pj-people {
    display: flex;
    align-items: center;
    flex-direction: column;
    height: 45% !important;
    .icon {
      font-size: 40px;
      margin-top: 32px;
      .van-icon {
        transform: rotate(45deg);
      }
    }
    .info {
      margin-top: 14px;
      color: #999999;
      font-size: 14px;
    }
    .sure {
      margin-top: 9px;
      color: #333333;
      font-size: 18px;
      font-weight: bold;
    }
    .btn-box {
      margin-top: 24px;
      padding: 0 15px;
      box-sizing: border-box;
      width: 100%;
      .van-button:first-child {
        margin-bottom: 15px;
      }
    }
  }
}
</style>
