<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="保单详情" left-arrow @click-left="$router.push({ name: 'Policies', query: { active: 1 } })" />
    </van-sticky>
    <div class="top-box">
      <van-cell>
        <template #title>
          <div class="cell-left">
            <div>{{ data.product?.name }}</div>
            <div>{{ data.order_no }}</div>
          </div>
        </template>
        <template #right-icon>
          <!-- <span class="status green-title" v-if="false">已出单</span>
          <span class="status yellow-title">已提交</span> -->
          <!-- <span class="status yellow-title">{{ data.status_text }}</span> -->
          <!-- 已退回、未提交 -->
          <span class="status-style color1" v-if="data.status === 0 || data.status === 10 || data.status === 13">
            {{ data.status_text }}
          </span>
          <!-- 已审核、已出单、已支付 -->
          <span class="status-style color2" v-if="data.status === 3 || data.status === 4 || data.status === 5">
            {{ data.status_text }}
          </span>
          <!-- 已作废、退保 -->
          <span class="status-style color3" v-if="data.status === 6 || data.status === 7">{{ data.status_text }}</span>
          <!-- 审核中、退保中、批改中 -->
          <span class="status-style color4" v-if="data.status === 2 || data.status === 8 || data.status === 9">
            {{ data.status_text }}
          </span>
          <!-- 已提交、待确认、待支付、暂存单 -->
          <span class="status-style color5" v-if="data.status === 1 || data.status === 11 || data.status === 12">
            {{ data.status_text }}
          </span>
        </template>
      </van-cell>
      <div>
        <van-cell class="before-none" title="主险" :value="data.product?.main_clause" :border="false" />
        <van-cell title="附加险" :value="data.product?.additional_clauses" :border="false" />
        <van-cell title="保险金额" :value="`￥${data.coverage}`" :border="false" />
        <van-cell title="保费" :value="`￥${data.premium}`" :border="false" />
      </div>
    </div>
    <div class="flex-box">
      <ul class="box-content">
        <template v-if="loggedId === data?.user?.id">
          <li
            class="item"
            v-if="data.status === 5"
            @click="$router.push({ name: 'InsureDomestic', query: { id: id, from: 'edit' } })"
          >
            <div class="wrapper">
              <div class="img-box yellow">
                <img src="@/assets/imgs/manage/edit.png" alt="" />
              </div>
              <div class="text-box">编辑</div>
            </div>
          </li>
          <li
            class="item"
            v-if="data.status === 13 && data?.tickets.find((ticket) => ticket.status === 4)"
            @click="
              $router.push({
                name: 'InsureDomestic',
                query: { id: id, from: 'edit', ticket_id: data?.tickets.find((ticket) => ticket.status === 4)?.id }
              })
            "
          >
            <div class="wrapper">
              <div class="img-box yellow">
                <img src="@/assets/imgs/manage/edit.png" alt="" />
              </div>
              <div class="text-box">补充资料</div>
            </div>
          </li>
          <li class="item" @click="surrender.actionSheet = true" v-if="data.status === 5">
            <div class="wrapper">
              <div class="img-box red">
                <img src="@/assets/imgs/manage/bank.png" alt="" />
              </div>
              <div class="text-box">退保</div>
            </div>
          </li>
          <li v-if="isUnPaid" class="item" @click="handleContinuePayment">
            <div class="wrapper">
              <div class="img-box green">
                <img src="@/assets/imgs/manage/continue.png" alt="" />
              </div>
              <div class="text-box">继续支付</div>
            </div>
          </li>
          <li class="item" @click.stop="$router.push({ name: 'InsureDomestic', query: { id: id, from: 'copy' } })">
            <div class="wrapper">
              <div class="img-box green">
                <img src="@/assets/imgs/manage/copy.png" alt="" />
              </div>
              <div class="text-box">复制保单</div>
            </div>
          </li>
          <template v-if="data.status === 0">
            <li class="item" @click="confirmDestroy">
              <div class="wrapper">
                <div class="img-box red">
                  <img src="@/assets/imgs/manage/destroy.png" alt="" />
                </div>
                <div class="text-box">作废</div>
              </div>
            </li>
          </template>
          <li
            v-if="[0, 10, 13].includes(data.status)"
            class="item"
            @click="$router.push({ name: 'InsureDomestic', query: { id: id, from: 'continue' } })"
          >
            <div class="wrapper">
              <div class="img-box skyblue">
                <img src="@/assets/imgs/manage/continue.png" alt="" />
              </div>
              <div class="text-box">继续投保</div>
            </div>
          </li>
        </template>
        <li class="item" v-if="data.status === 5" @click="downloadFile">
          <div class="wrapper">
            <div class="img-box blue">
              <img src="@/assets/imgs/manage/write.png" alt="" />
            </div>
            <div class="text-box">电子保单</div>
          </div>
        </li>
      </ul>
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">系统信息</span>
        </template>
      </van-cell>
      <van-cell title="保单号" :value="data.policy_no" />
      <van-cell title="出单时间" :value="data.issued_at" />
      <van-cell title="投保单号" :value="data.apply_no" />
      <van-cell title="流水号" :value="data.order_no" />
      <van-cell title="投保用户" :value="data.user?.name" />
      <van-cell title="投保时间" :value="data.created_at" />
      <van-cell title="第三方标识" :value="data.trade_order_no" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">产品信息</span>
        </template>
      </van-cell>
      <van-cell title="标的" :value="data.detail?.subject?.name" />
      <van-cell title="保险公司" :value="data.company?.name" />
      <van-cell title="保险产品" :value="data.product?.name" />
      <van-cell title="保险代码" :value="data.product?.code" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">基本信息</span>
        </template>
      </van-cell>
      <van-cell title="投保人" :value="data.policyholder" />
      <van-cell title="地址" :value="data.policyholder_address" />
      <van-cell title="被保人" :value="data.insured" />
      <van-cell title="地址" :value="data.insured_address" />
      <van-cell title="投保人电话" :value="data.policyholder_phone_number" />
      <van-cell title="被保人电话" :value="data.insured_phone_number" />
      <van-cell title="第三方标识" :value="data.trade_order_no" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">货物信息</span>
        </template>
      </van-cell>
      <van-cell title="货物类别" :value="data.detail?.goods_type?.name" />
      <van-cell title="装载方式" :value="data.detail?.loading_method?.name" />
      <van-cell title="运输方式" :value="data.detail?.transport_method?.name" />
      <van-cell title="包装" :value="data.detail?.packing_method?.name" />
      <van-cell title="货物名称" :value="data.detail?.goods_name" />
      <van-cell title="数量规格" :value="data.detail?.goods_amount" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">运输信息</span>
        </template>
      </van-cell>
      <van-cell title="运单号" :value="data.detail?.waybill_no" />
      <van-cell title="发票号" :value="data.detail?.invoice_no" />
      <van-cell title="运输工具号" :value="data.detail?.transport_no" />
      <van-cell title="起运日期" :value="data.detail?.shipping_date" />
      <van-cell title="倒签保函">
        <template #right-icon>
          <a
            v-if="data.detail?.anti_dated_file"
            style="`color: ${primaryColor}`"
            :href="data.detail?.anti_dated_file"
            target="_blank"
          >
            点击查看
          </a>
          <span v-else>-</span>
        </template>
      </van-cell>
      <van-cell title="起运地" :value="data.detail?.departure" />
      <van-cell title="目的地" :value="data.detail?.destination" />
      <van-cell title="中转地" :value="data.detail?.transmit" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">保险内容</span>
        </template>
      </van-cell>
      <van-cell title="主条款" :label="data.detail?.main_clause" />
      <van-cell title="免赔" :label="data.detail?.deductible" />
      <van-cell title="特别约定" :label="data.detail?.special" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">费用信息</span>
        </template>
      </van-cell>
      <van-cell title="保险金额(元)" :value="data.coverage" />
      <van-cell title="保费(元)" :value="data.premium" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">其他</span>
        </template>
      </van-cell>
      <van-cell title="备注" :label="data.remark" />
      <van-cell title="工作编号" :label="data.sticky_note" />
      <van-cell title="投保附件">
        <template #right-icon>
          <a
            v-if="data.detail?.custom_file"
            style="`color: ${primaryColor}`"
            :href="data.detail?.custom_file"
            target="_blank"
          >
            点击查看
          </a>
          <span v-else>-</span>
        </template>
      </van-cell>
    </div>
    <!-- 退保弹窗 -->
    <van-action-sheet v-model="surrender.actionSheet" title="退保原因">
      <div class="content-reason">
        <van-field
          v-model="surrender.reason"
          rows="2"
          autosize
          label="退保原因"
          type="textarea"
          maxlength="50"
          placeholder="请输入退保原因"
          show-word-limit
        />
        <div class="reason-box">
          <van-button type="primary" block :color="primaryColor" @click="confirmSurrender">确认退保</van-button>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { manageDetail, surrender, downloadFrom, destroyDraft } from '@/apis/manage'
import { userKey } from '@/config'

export default {
  data() {
    return {
      id: this.$route.params.id,
      data: {},
      surrender: {
        actionSheet: false,
        reason: ''
      }
    }
  },
  computed: {
    loggedId() {
      const user = JSON.parse(localStorage.getItem(userKey) || '{}')

      return user?.id
    },
    primaryColor() {
      return this.$store.state.primaryColor
    },
    isUnPaid() {
      return (
        [12].includes(this.data?.status) && this.data?.payments.find((payment) => payment.is_paid === 0)?.callback_data
      )
    }
  },
  created() {
    this.getPageData()
  },
  methods: {
    getPageData() {
      manageDetail(this.id).then((r) => {
        this.data = r.data.data
      })
    },
    // 退保原因
    confirmSurrender() {
      surrender(this.id, { reason: this.surrender.reason }).then((r) => {
        this.$toast.success('退保申请成功')

        this.getPageData()

        this.surrender.actionSheet = false
      })
    },
    confirmDestroy() {
      destroyDraft(this.id).then(() => {
        this.$toast.success('作废成功')

        this.$router.go(-1)
      })
    },
    // 下载保单
    downloadFile() {
      this.$download(downloadFrom(this.id))
    },
    handleContinuePayment() {
      const unpaidPayment = this.data?.payments?.find((payment) => payment.is_paid === 0)
      if (unpaidPayment && unpaidPayment.callback_data) {
        window.location.href = unpaidPayment.callback_data.payment_url
      } else {
        this.$message.warning('没有找到有效的支付链接')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .top-box {
    margin: 15px 0 34px;
    padding-bottom: 80px;
    background-color: #fff;
    .van-cell {
      background-color: #ffffff;
      // &:not(&:first-child) {
      padding-bottom: 0;
      // }
      &:first-child:before {
        content: '';
        display: inline-block;
        width: 4px;
        background-color: red;
        margin-right: 8px;
      }
      .cell-left {
        div:first-child {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
        div:last-child {
          color: #333;
          font-size: 16px;
          font-weight: 400;
        }
      }
      .status {
        font-size: 14px;
        font-weight: bold;
      }
      .green-title {
        color: #5cc374;
      }
      .yellow-title {
        color: #f29925;
      }
    }
  }
  .flex-box {
    position: absolute;
    top: 280px;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 0 15px;
    .box-content {
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      .item {
        flex: 1;
        padding: 15px 0;
        text-align: center;
        .wrapper {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: center;
          height: 50px;
          .img-box {
            height: 34px;
            width: 34px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 16px;
              // height: 16px;
              background-size: contain;
            }
          }

          .text-box {
            color: #333;
            font-size: 12px;
          }
        }
      }
    }
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
    img {
      width: 12px;
      margin-right: 5px;
      position: relative;
      top: 2px;
    }
    .word {
      font-size: 16px;
      font-weight: bold;
    }
    .submit-btn {
      padding: 15px;
    }
  }
}
// 文件信息

.file-info {
  /deep/ .van-cell__title {
    color: #999;
    font-size: 14px;
  }
  .van-cell {
    & > span {
      color: var(--primary-color);
      font-size: 14px;
    }
  }
}
// 背景色
.yellow {
  background-color: #ffb85b;
}
.red {
  background-color: #ff5656;
}
.green {
  background-color: #7cc865;
}
.blue {
  background-color: #6f94ff;
}
.skyblue {
  background-color: #21cae3;
}
// 已退回、未提交
.color1 {
  color: #d0021b;
}
//已审核、已出单、已支付
.color2 {
  color: #5cc374;
}
//已作废、已退保
.color3 {
  color: #979797;
}
//审核中、退保中、批改中
.color4 {
  color: #fb7203;
}
//已提交、待确认、待支付、暂存单
.color5 {
  color: #f29925;
}
.van-overlay {
  z-index: 20;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}
.reason-box {
  padding: 15px 15px 30px;
}
/deep/ .before-none {
  &::before {
    content: none !important;
  }
}
</style>
