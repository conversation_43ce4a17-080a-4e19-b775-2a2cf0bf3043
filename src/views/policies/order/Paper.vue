<template>
  <div style="background: #fafafa">
    <van-sticky>
      <van-nav-bar title="纸质保单" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">保单信息</span>
        </template>
      </van-cell>
      <van-cell title="保单号" :value="policy?.policy_no" />
      <van-cell title="投保人" :value="policy?.policyholder" />
      <van-cell title="投保人地址" :value="policy?.policyholder_address || '-'" />
      <van-cell title="被保人" :value="policy?.insured" />
      <van-cell title="被保人地址" :value="policy?.insured_address || '-'" />
      <van-cell title="起运日期打印格式" :value="shippingDatePrintFormat" />
    </div>

    <van-form @submit="handleSubmit" style="margin-top: 10px; margin-bottom: 1rem">
      <van-field
        readonly
        clickable
        left-icon="label-o"
        right-icon="arrow"
        name="recently"
        :value="recently"
        label="历史收件人"
        placeholder="选择历史收件人"
        @click="showRecentlyPicker = true"
      />
      <van-popup v-model="showRecentlyPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="recentlyColumns"
          @confirm="onRecentlyConfirm"
          @cancel="showRecentlyPicker = false"
        />
      </van-popup>

      <van-field
        v-model="form.recipient"
        left-icon="user-o"
        name="Recipient"
        label="收件人"
        required
        placeholder="请输入收件人"
        :rules="[{ required: true, message: '收件人不能为空' }]"
      />

      <van-field
        v-model="form.phone"
        left-icon="user-o"
        name="Phone"
        label="收件电话"
        required
        placeholder="请输入收件电话"
        :rules="[{ required: true, message: '收件电话不能为空' }]"
      />

      <van-field
        readonly
        clickable
        left-icon="logistics"
        right-icon="arrow"
        name="logistics"
        :value="expressType"
        label="快递类型"
        required
        placeholder="请选择快递类型"
        @click="showExpressTypePicker = true"
      />
      <van-popup v-model="showExpressTypePicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="expressColumns"
          @confirm="onExpressConfirm"
          @cancel="showExpressPicker = false"
        />
      </van-popup>

      <van-field
        v-model="form.address"
        type="textarea"
        rows="3"
        autosize
        left-icon="guide-o"
        name="Address"
        label="收件地址"
        placeholder="请输入收件地址"
        required
        :rules="[{ required: true, message: '收件地址不能为空' }]"
      />

      <van-field
        v-model="form.remark"
        type="textarea"
        rows="3"
        autosize
        left-icon="guide-o"
        name="Remark"
        label="备注"
        placeholder="请输入备注"
      />

      <div style="margin: 16px">
        <van-button :color="primaryColor" block type="info" native-type="submit"> 提交申请 </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import { manageDetail } from '@/apis/manage'
import { getPolicyPapers, applyPolicyPaper } from '@/apis/policy'
import dayjs from 'dayjs'

export default {
  name: 'PolicyPaper',
  data() {
    return {
      policy: {},
      recently: '',
      recentlyColumns: [],
      showRecentlyPicker: false,

      expressType: '',
      showExpressTypePicker: false,
      expressColumns: [
        { text: '普通快递', value: 1 },
        { text: '顺丰快递', value: 2 },
        { text: '自取保单', value: 3 }
      ],
      form: {
        recipient: '',
        address: '',
        type: '',
        phone: '',
        remark: ''
      }
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    },
    shippingDatePrintFormat() {
      return parseInt(this.policy?.detail?.shipping_date_print_format, 10) === 1
        ? dayjs(this.policy?.detail?.shipping_date).format('MMM.DD, YYYY')
        : 'AS PER B/L'
    }
  },
  created() {
    manageDetail(this.$route.params.id).then((r) => {
      this.policy = r.data.data
    })

    getPolicyPapers().then((r) => {
      r.data.data.forEach((item) => {
        this.recentlyColumns.push({
          text: item.recipient,
          value: item
        })
      })
    })
  },
  methods: {
    onRecentlyConfirm(value) {
      this.showRecentlyPicker = false
      if (value?.value) {
        this.form = value.value
        this.recently = value.text
      }
    },
    onExpressConfirm(value) {
      this.showExpressTypePicker = false
      if (value?.value) {
        this.form.type = value.value
        this.expressType = value.text
      }
    },
    handleSubmit() {
      applyPolicyPaper(this.$route.params.id, this.form).then(() => {
        this.$toast.success('提交成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style lang="scss">
.bg-gray {
  background-color: #fafafa;
}
.item-wrapper {
  background-color: #fff;
  margin-top: 10px;
  img {
    width: 12px;
    margin-right: 5px;
    position: relative;
    top: 2px;
  }
  .word {
    font-size: 16px;
    font-weight: bold;
  }
  .submit-btn {
    padding: 15px;
  }
}
</style>
