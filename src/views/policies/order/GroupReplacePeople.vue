<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="人员替换" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <van-form @submit="onSubmit">
      <van-field
        v-model="name"
        name="name"
        label="姓名"
        placeholder="请填写姓名"
        :rules="[{ required: true, message: '请填写姓名' }]"
      />
      <van-field
        v-model="idcard_no"
        name="idcard_no"
        label="身份证号"
        placeholder="请输入身份证号"
        :rules="[{ required: true, message: '请输入身份证号' }]"
      />
      <van-field
        v-model="mobile"
        name="mobile"
        label="手机号"
        placeholder="请输入手机号"
        :rules="[{ required: false, message: '请输入手机号' }]"
      />

      <div style="margin: 16px">
        <van-button
          :color="primaryColor"
          style="
             {
              border-radius: 4px;
            }
          "
          block
          type="info"
          native-type="submit"
          >确认替换</van-button
        >
      </div>
    </van-form>
  </div>
</template>

<script>
import { replacePeople } from '@/apis/manage'

export default {
  data() {
    return {
      name: '',
      idcard_no: '',
      mobile: ''
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  methods: {
    onSubmit(values) {
      console.log('submit', values)
      const _data = {
        ...this.$route.params,
        ...values,
        action: 'replace'
      }
      replacePeople(_data).then((r) => {
        this.$toast('修改成功')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left,
  /deep/ .van-icon-search {
    color: #333;
  }
}
</style>
