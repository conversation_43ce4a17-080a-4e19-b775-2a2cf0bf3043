<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="添加人员" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>
    <van-form @submit="onSubmit">
      <van-field
        v-model="form.name"
        label="姓名"
        placeholder="请填写姓名"
        :rules="[{ required: true, message: '请填写姓名' }]"
      />
      <van-field
        v-model="form.idcard_no"
        label="身份证号"
        placeholder="请输入身份证号"
        :rules="[{ required: true, message: '请输入身份证号' }]"
      />
      <van-field
        v-model="form.mobile"
        label="手机号"
        placeholder="请输入手机号"
        :rules="[{ required: false, message: '请输入手机号' }]"
      />
      <van-field
        readonly
        clickable
        :value="value1"
        label="职位类别"
        placeholder="点击职位类别"
        @click="showPicker = true"
        right-icon="arrow-down"
        :rules="[{ required: true, message: '请输入职位名称' }]"
      />
      <van-field
        v-model="form.job_name"
        label="职位名称"
        placeholder="请输入职位名称"
        :rules="[{ required: true, message: '请输入职位名称' }]"
      />
      <van-popup v-model="showPicker" position="bottom">
        <van-search v-model="searchText" @input="onSearch" />
        <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="showPicker = false" />
      </van-popup>

      <div style="margin: 16px">
        <van-button
          :color="primaryColor"
          style="
             {
              border-radius: 4px;
            }
          "
          block
          type="info"
          native-type="submit"
          >提交</van-button
        >
      </div>
    </van-form>
  </div>
</template>

<script>
import { addPeople, getDetail } from '@/apis/manage'

export default {
  data() {
    return {
      value: '',
      value1: '',
      username: '',
      password: '',
      showPicker: false,
      fullColumns: [],
      columns: [],
      searchText: '',
      form: {
        name: '',
        mobile: '',
        idcard_no: '',
        job_name: '',
        job_id: ''
      }
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  watch: {
    'form.job_id'(value) {
      if (value) {
        this.form.job_name = this.columns.find((e) => e.id === value)?.name
      }
    }
  },
  mounted() {
    getDetail(this.$route.params.id).then((r) => {
      const _data = r.data.data.plan.jobs
      _data.forEach((item) => {
        item.text = item.name
        item.value = item.id
      })
      this.columns = r.data.data.plan.jobs
      this.fullColumns = r.data.data.plan.jobs
    })
  },
  methods: {
    onSearch() {
      if (!this.searchText) {
        this.columns = JSON.parse(JSON.stringify(this.fullColumns))
      } else {
        this.columns = this.fullColumns.filter((e) => e.name.indexOf(this.searchText) > -1)
      }
    },
    onSubmit(values) {
      const _data = Object.assign({}, this.form, { action: 'append' }, { policy_group_id: this.$route.params.id })
      addPeople(_data).then((r) => {
        this.$toast('人员操作成功')
        this.$router.go(-1)
      })
    },
    onConfirm(values) {
      this.form.job_id = values.value
      this.value1 = values.text
      this.showPicker = false
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left,
  /deep/ .van-icon-search {
    color: #333;
  }
}
</style>
