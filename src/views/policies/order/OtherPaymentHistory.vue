<template>
  <div class="payment-history">
    <van-sticky>
      <van-nav-bar title="保单详情" left-arrow @click-left="$router.go(-1)" />
    </van-sticky>

    <div class="container bg-gray">
      <div class="b-card" v-for="history in histories" :key="history.id">
        <div class="b-card__header">
          <div class="b-card__price">¥ {{ history.amount }}</div>
          <div class="b-card__header__status" :class="history.status | statusClass">
            <span class="b-card__dot"></span>
            {{ history.status | statusText }}
          </div>
        </div>
        <div class="b-card__content">
          <ul>
            <li>
              <span>支付流水号</span><span>{{ history.transaction_no }}</span>
            </li>
            <li>
              <span>支付日期</span><span>{{ history.created_at | date }}</span>
            </li>
          </ul>
        </div>
      </div>
      <van-empty v-if="histories.length <= 0" description="暂无记录" />
    </div>
  </div>
</template>

<script>
import { paymentHistory } from '@/apis/policy'
import dayjs from 'dayjs'

export default {
  name: 'OtherPaymentHistory',
  data() {
    return {
      histories: []
    }
  },
  filters: {
    date(value) {
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
    },
    statusText(value) {
      const statuses = {
        1: '已支付',
        2: '已到帐',
        3: '已拒绝/已退回',
        4: '已提交'
      }

      return statuses[value] || '其他'
    },
    statusClass(value) {
      return value === 2 ? 'b-card__green' : 'b-card__orange'
    }
  },
  created() {
    paymentHistory(this.$route.params.id).then((r) => {
      this.histories = r.data.data
    })
  }
}
</script>

<style lang="less" scoped>
.payment-history {
  min-height: 100vh;
  .container {
    min-height: calc(100vh - 48px);
    padding: 15px;

    .b-card {
      background-color: #ffffff;
      border-radius: 4px;
      box-shadow: 4px 4px 20px 4px rgba(0, 0, 0, 0.05);
      padding: 15px;

      .b-card__header {
        display: flex;
        align-content: center;
        padding-bottom: 15px;
        font-size: 14px;

        div {
          flex: 1;
        }
        .b-card__price {
          font-size: 14px;
        }

        .b-card__header__status {
          text-align: right;
          font-size: 14px;
        }

        .b-card__dot {
          display: inline-flex;
          width: 6px;
          height: 6px;
          border-radius: 50%;
        }

        .b-card__green {
          color: #5cc374;

          .b-card__dot {
            background: #5cc374;
          }
        }

        .b-card__orange {
          color: #ff960e;
        }
      }

      .b-card__content {
        border-top: 1px solid #eee;
        padding-top: 15px;
        font-size: 14px;
        color: #999;
        font-weight: 400;

        ul,
        li {
          list-style-type: none;
        }

        li {
          display: flex;
          align-content: center;
          padding: 11px 0 11px 0;

          span:first-child {
            width: 30%;
          }
        }
      }
    }
  }

  .bg-gray {
    background-color: #ededed;
  }
}
</style>
