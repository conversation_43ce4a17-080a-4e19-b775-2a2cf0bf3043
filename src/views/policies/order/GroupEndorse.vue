<template>
  <div style="min-height: 100vh; background-color: #fafafa;">
    <van-sticky>
      <van-nav-bar title="批单管理" left-arrow @click-left="$router.push({name: 'GroupDetail', params: {id: $route.params.id}})"/>
    </van-sticky>

    <section class="endorses">
      <van-cell-group v-for="endorse in endorses" :key="endorse.id" inset class="__cell_group">
        <van-cell>
          <template #title>
            <van-cell class="__title">
              <template>
                <span class="__title">{{ endorse.endorse_no || '暂无' }}</span>
                <van-button color="#ff7f4b" icon="down" size="mini" style="border-radius: 4px; float: right;" @click="handleDownload(endorse)">下载批单</van-button>
              </template>
            </van-cell>
          </template>
          <template #label>
            <ul class="__label">
              <li>
                <div class="__label_t">批次号</div>
                <div class="__label_c">{{ endorse.batch_no }}</div>
              </li>
              <li>
                <div class="__label_t">添加时间</div>
                <div class="__label_c">{{ endorse.created_at }}</div>
              </li>
              <li>
                <div class="__label_t">备注</div>
                <div class="__label_c">{{ endorse.content || '-' }}</div>
              </li>
              <li>
                <div class="__label_t">状态</div>
                <div class="__label_c" :class="{ 'passed': endorse.status === 1, 'reject': endorse.status === 2 }">{{ endorse.status | statusText }}</div>
              </li>
            </ul>
          </template>
        </van-cell>
      </van-cell-group>
    </section>
  </div>
</template>

<script>
import { groupEndorses, buildEndorseDownloadHref } from "@/apis/manage"

export default {
  name: 'GroupEndorses',
  data() {
    return {
      endorses: []
    }
  },
  filters: {
    statusText(status) {
      return {
        0: '审核中',
        1: '已通过',
        2: '已退回',
        3: '已提交'
      }[status]
    }
  },
  created() {
    groupEndorses(this.$route.params.id).then(res => {
      this.endorses = res.data.data
    })
  },
  methods: {
    handleDownload(endorse) {
      this.$download(buildEndorseDownloadHref(endorse.id))
    }
  }
}
</script>

<style lang="less">
.endorses {
  margin-top: 15px;
  .__cell_group  {
    margin-top: 15px;
    box-shadow: 4px 4px 20px 0px rgba(0, 0, 0, 0.05);
  }

  .__title {
    padding: 0 0 10px 0;
  }

  .__label {
    padding: 0;
    margin: 0;
    li {
      padding: 5px 0;
      font-size: 14px;

      div {
        height: auto;
        display: inline-block;
      }

      .__label_t {
        min-width: 80px;
      }

      .__label_c {
        max-width: calc(100% - 80px);
      }

      .passed {
        color: #4caf50;
      }

      .reject {
        color: #ff7f4b;
      }
    }
  }
}
</style>
