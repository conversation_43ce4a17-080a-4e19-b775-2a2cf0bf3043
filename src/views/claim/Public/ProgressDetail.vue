<template>
  <div class="freightOrder">
    <van-sticky>
      <van-nav-bar title="案件详情" left-arrow @click-left="$router.push({ name: 'PublicClaimProgress' })" />
    </van-sticky>
    <div class="top-box">
      <van-cell>
        <template #title>
          <div class="cell-left">
            <div>{{ data.case_no }}</div>
            <div>{{ data?.status_text?.slice(data?.status_text?.lastIndexOf('>') + 1) }}</div>
          </div>
        </template>
        <template #right-icon>
          <span class="status-style color1">
            <div>{{ getPolicyType }}</div>
          </span>
        </template>
      </van-cell>
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">案件信息</span>
        </template>
      </van-cell>
      <van-cell title="保单号" :value="data.policy_no" />
      <van-cell title="保司案件号" :value="data.external_case_no" />
      <van-cell title="出险原因" :value="data.loss_reason" />
      <van-cell title="出险时间" :value="data.date_of_loss" />
      <van-cell title="出险地点" :value="data.loss_location" />
      <van-cell title="出险详细地址" :value="data.loss_address" />
      <van-cell title="损失类型" :value="data.loss_category_text" />
      <van-cell title="损失金额" :value="data.loss_amount" />
      <van-cell title="损失金额币种" :value="data.loss_amount_currency" />
      <van-cell title="损失经过" :value="data.loss_detail" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">报案人信息</span>
        </template>
      </van-cell>
      <van-cell title="报案人" :value="data.claimant" />
      <van-cell title="报案人邮箱" :value="data.claimant_email" />
      <van-cell title="报案人电话" :value="data.claimant_phone_number" />
      <van-cell title="报案时间" :value="data.created_at" />
    </div>
    <div class="item-wrapper">
      <van-cell>
        <template #title>
          <img src="@/assets/imgs/insure/book.png" alt="" />
          <span class="word">附件信息</span>
        </template>
      </van-cell>

      <template v-for="(attachment, idx) in data?.attachments">
        <van-cell
          :key="idx"
          :title="attachment?.primary_category + '-' + attachment?.secondary_category + '-' + attachment?.name"
        >
          <template #right-icon>
            <a v-if="attachment" style="`color: ${primaryColor}`" :href="attachment.href" target="_blank"> 点击查看 </a>
            <span v-else>-</span>
          </template>
        </van-cell>
      </template>
    </div>
    <van-form scroll-to-error>
      <van-collapse accordion v-if="![0, 4, 5, 7].includes(data?.status)" v-model="contactCollapse">
        <van-collapse-item title="添加附件" name="1">
          <!-- 选择附件类别 -->
          <van-field
            v-model="attachmentPicker.value"
            is-link
            readonly
            label="文件类别"
            placeholder="请选择文件类别"
            @click="attachmentPicker.show = true"
          />
          <van-popup v-model="attachmentPicker.show" round position="bottom">
            <van-cascader
              v-model="attachmentPicker.value"
              title="请选择文件类别"
              :options="fileCategories"
              @close="attachmentPicker.show = false"
              @finish="attachmentPickerFinish"
            />
          </van-popup>

          <!-- 添加附件 -->
          <van-field is-link readonly label="上传附件">
            <template #button>
              <van-uploader
                v-model="fileList"
                multiple
                :preview-image="false"
                :after-read="uploadAttachment"
                :disabled="!attachmentPicker.value"
                :max-size="2 * 1024 * 1024"
              >
                <van-button icon="plus" :color="primaryColor" size="small" :disabled="!attachmentPicker.value"
                  >上传文件</van-button
                >
              </van-uploader>
            </template>
          </van-field>
          <van-uploader style="padding: 0 5%" v-if="fileList.length > 0" v-model="fileList" multiple disabled>
            <template #preview-cover="{ file }">
              <div class="preview-cover van-ellipsis">{{ file.text }}</div>
            </template>
          </van-uploader>
        </van-collapse-item>
      </van-collapse>
      <div class="item-wrapper none-r">
        <div class="btn-box">
          <van-button
            block
            :color="primaryColor"
            size="small"
            :disabled="!fileList.length > 0"
            v-if="![0, 4, 5, 7].includes(data?.status)"
            @click="handleSubmit(data.id)"
            >保存</van-button
          >
          <van-button
            block
            :color="primaryColor"
            size="small"
            v-if="[-1, 1, 2, 3].includes(data?.status) && !data?.apply_cancellation_at"
            @click="handleCancel(data.id)"
            >撤销报案</van-button
          >
        </div>
      </div>
    </van-form>
  </div>
</template>

<script>
import * as claimApi from '@/apis/claim'
import { userKey } from '@/config'

export default {
  data() {
    return {
      id: this.$route.params.id,
      data: {},
      surrender: {
        actionSheet: false,
        reason: ''
      },
      attachmentPicker: {
        show: false,
        value: '',
        data: []
      },
      contactCollapse: '',
      fileList: [],
      maxSize: 8
    }
  },
  computed: {
    loggedId() {
      const user = JSON.parse(localStorage.getItem(userKey) || '{}')

      return user?.id
    },
    primaryColor() {
      return this.$store.state.primaryColor
    },
    getPolicyType() {
      const types = {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任险',
        4: '其他险种',
        5: '雇主责任险'
      }

      return types[this.data.policy_type]
    },
    fileCategories() {
      switch (this.data?.policy_type) {
        case 1:
        case 2:
        case 3:
          return [
            {
              text: '运输单证',
              value: '运输单证',
              children: [
                { text: '提单', value: '提单' },
                { text: '运输合同/运单', value: '运输合同/运单' },
                { text: '装车清单', value: '装车清单' },
                { text: '运输经营许可证', value: '运输经营许可证' },
                { text: '驾驶证', value: '驾驶证' },
                { text: '行驶证', value: '行驶证' },
                { text: '车辆道路运输证', value: '车辆道路运输证' },
                { text: '监装报告', value: '监装报告' },
                { text: '设备交接单EIR', value: '设备交接单EIR' },
                { text: '签收交接单', value: '签收交接单' }
              ]
            },
            {
              text: '货物单证',
              value: '货物单证',
              children: [
                { text: '购货合同', value: '购货合同' },
                { text: '形式发票', value: '形式发票' },
                { text: '装箱单', value: '装箱单' },
                { text: '报关单', value: '报关单' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '现场照片视频类',
              value: '现场照片视频类',
              children: [
                {
                  text: '运输工具（全貌、船舱、车厢的密封情况、受损状况、船名、车牌号）',
                  value: '运输工具（全貌、船舱、车厢的密封情况、受损状况、船名、车牌号）'
                },
                {
                  text: '集装箱（全貌、集装箱号、内外状况、受损细节）',
                  value: '集装箱（全貌、集装箱号、内外状况、受损细节）'
                },
                {
                  text: '标的货物（全貌、包装、运输标志、防震动、防倾、温控标签、绑扎和积载、铭牌、受损细节）',
                  value: '标的货物（全貌、包装、运输标志、防震动、防倾、温控标签、绑扎和积载、铭牌、受损细节）'
                },
                { text: '发货现场（装箱情况、标的物状态）', value: '发货现场（装箱情况、标的物状态）' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定责类',
              value: '定责类',
              children: [
                { text: '交通责任认定书', value: '交通责任认定书' },
                { text: '火灾证明', value: '火灾证明' },
                { text: '破损单', value: '破损单' },
                { text: '理货报告', value: '理货报告' },
                { text: '承运人出具的事故说明', value: '承运人出具的事故说明' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定损类',
              value: '定损类',
              children: [
                { text: '报损清单', value: '报损清单' },
                { text: '核验报告', value: '核验报告' },
                { text: '鉴定报告', value: '鉴定报告' },
                { text: '其他', value: '其他' }
              ]
            }
          ]
        // 其他
        case 4:
          return [
            {
              text: '定责类',
              value: '定责类',
              children: [
                { text: '火灾证明', value: '火灾证明' },
                { text: '交通事故责任认定书', value: '交通事故责任认定书' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定损类',
              value: '定损类',
              children: [
                { text: '报损明细', value: '报损明细' },
                { text: '损失证明', value: '损失证明' },
                { text: '检查报告', value: '检查报告' },
                { text: '维修报价', value: '维修报价' },
                { text: '其他', value: '其他' }
              ]
            }
          ]
        // 雇主
        case 5:
          return [
            {
              text: '定责类',
              value: '定责类',
              children: [
                { text: '受伤员工劳动合同（注明工种）', value: '受伤员工劳动合同（注明工种）' },
                { text: '病历、检验报告、出院小结', value: '病历、检验报告、出院小结' },
                { text: '工伤认定书、伤残鉴定书、死亡证明', value: '工伤认定书、伤残鉴定书、死亡证明' },
                { text: '交通责任认定书、交通事故调解书', value: '交通责任认定书、交通事故调解书' },
                { text: '事故情况说明', value: '事故情况说明' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定损类',
              value: '定损类',
              children: [
                { text: '医疗费用明细', value: '医疗费用明细' },
                { text: '医疗费用发票/收据', value: '医疗费用发票/收据' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '现场照片视频类',
              value: '现场照片视频类',
              children: [
                { text: '事故现场', value: '事故现场' },
                { text: '伤者情况', value: '伤者情况' },
                { text: '其他', value: '其他' }
              ]
            }
          ]
        default:
          return []
      }
    }
  },
  created() {
    this.getPageData()
  },
  methods: {
    getPageData() {
      claimApi.fetchPublicCaseDetail(this.id).then((r) => {
        this.data = r.data.data
      })
    },
    // 退保原因
    confirmSurrender() {
      surrender(this.id, { reason: this.surrender.reason }).then((r) => {
        this.$toast.success('退保申请成功')

        this.getPageData()

        this.surrender.actionSheet = false
      })
    },
    confirmDestroy() {
      destroyDraft(this.id).then(() => {
        this.$toast.success('作废成功')

        this.$router.go(-1)
      })
    },
    // 下载保单
    downloadFile() {
      this.$download(downloadFrom(this.id))
    },
    attachmentPickerFinish(data) {
      this.attachmentPicker.show = false
      this.attachmentPicker.data = []
      this.attachmentPicker.value = data.selectedOptions.map((option) => option.text).join('/')
      data.selectedOptions.map((option) => this.attachmentPicker.data.push(option.text))
    },
    uploadAttachment(file) {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      if (file.file.size / 1024 / 1024 > this.maxSize) {
        this.$toast.fail(`文件大小不能超过 ${this.maxSize} MB`)
        this.fileList.pop(file)
        return
      }

      if (this.attachmentPicker.length === 0) {
        this.$toast.fail('请先选择文件类别')
        return
      }
      file.name = file.file.name
      file.file.text = this.attachmentPicker.value + '-' + file.name
      file.category = {
        primary: this.attachmentPicker.data[0],
        secondary: this.attachmentPicker.data[1]
      }
      loading.clear()
    },
    async handleSubmit(id) {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      try {
        for (const f of this.fileList) {
          await claimApi.publicUploadAttachment(id, {
            primary_category: f.category.primary,
            secondary_category: f.category.secondary,
            name: f.name,
            file: f.file
          })
        }
        location.reload()
        this.$toast.success('上传附件成功')
      } catch {
        this.$toast.fail('上传附件失败')
      }
      loading.clear()
    },
    async handleCancel(id) {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      try {
        await claimApi.publicCancelClaim(id)
        location.reload()
        this.$toast.success('撤销报案成功')
      } catch {
        this.$toast.fail('撤销报案失败')
      }
      loading.clear()
    }
  }
}
</script>

<style lang="less" scoped>
.freightOrder {
  min-height: 100vh;
  padding-bottom: 50px;
  background-color: #fafafa;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .top-box {
    margin: 15px 0 15px;
    background-color: #fff;
    .van-cell {
      background-color: #ffffff;
      // &:not(&:first-child) {
      padding-bottom: 0;
      // }
      &:first-child:before {
        content: '';
        display: inline-block;
        width: 4px;
        background-color: red;
        margin-right: 8px;
      }
      .cell-left {
        div:first-child {
          color: #333;
          font-size: 18px;
          font-weight: bold;
        }
        div:last-child {
          color: #333;
          font-size: 16px;
          font-weight: 400;
        }
      }
      .status {
        font-size: 14px;
        font-weight: bold;
      }
      .green-title {
        color: #5cc374;
      }
      .yellow-title {
        color: #f29925;
      }
    }
  }
  .flex-box {
    left: 0;
    right: 0;
    z-index: 10;
    padding: 0 15px;
    .box-content {
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      .item {
        flex: 1;
        padding: 15px 0;
        text-align: center;
        .wrapper {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: center;
          height: 50px;
          .img-box {
            height: 34px;
            width: 34px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 16px;
              // height: 16px;
              background-size: contain;
            }
          }

          .text-box {
            color: #333;
            font-size: 12px;
          }
        }
      }
    }
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
    img {
      width: 12px;
      margin-right: 5px;
      position: relative;
      top: 2px;
    }
    .word {
      font-size: 16px;
      font-weight: bold;
    }
    .submit-btn {
      padding: 15px;
    }
  }
}
// 文件信息

.btn-box {
  padding: 10px 15px;
  .van-button {
    margin-bottom: 10px;
  }
}

.file-info {
  /deep/ .van-cell__title {
    color: #999;
    font-size: 14px;
  }
  .van-cell {
    & > span {
      color: var(--primary-color);
      font-size: 14px;
    }
  }
}
// 背景色
.yellow {
  background-color: #ffb85b;
}
.red {
  background-color: #ff5656;
}
.green {
  background-color: #7cc865;
}
.blue {
  background-color: #6f94ff;
}
.skyblue {
  background-color: #21cae3;
}
// 已退回、未提交
.color1 {
  color: #979797;
}
.van-overlay {
  z-index: 20;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}
.reason-box {
  padding: 15px 15px 30px;
}
.box-bottom {
  padding: 0 0 50px;
}
/deep/ .before-none {
  &::before {
    content: none !important;
  }
}
</style>
