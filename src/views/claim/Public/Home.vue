<template>
  <div class="insure-box">
    <div class="square-box">
      <div class="item" @click="policyNoPicker.show = true">
        <h3 class="heading">我要报案</h3>
        <p class="slogan">
          审核精准，快速理赔， <br />
          全程跟进服务保障。
        </p>
        <van-icon name="phone" size="40" />
      </div>
      <van-popup v-model="policyNoPicker.show" position="bottom" style="padding-bottom: 10px">
        <div class="item-wrapper">
          <van-cell-group>
            <van-field v-model="policyNo" label="保单号" placeholder="请输入保单号" />
          </van-cell-group>
        </div>
        <div class="item-wrapper" style="padding-bottom: 10px">
          <div class="btn-box">
            <van-button block round :color="primaryColor" size="small" @click="handleSubmit">我要报案</van-button>
          </div>
        </div>
      </van-popup>
      <div class="item" @click="$router.push({ name: 'PublicClaimProgress' })">
        <h3 class="heading">案件进度查询</h3>
        <p class="slogan">
          在线查看案件事时状态， <br />
          流程简便，精准掌握案情
        </p>
        <van-icon name="service-o" size="40" />
      </div>
    </div>
  </div>
</template>

<script>
import { searchByPolicyNo } from '@/apis/policy'
export default {
  name: 'Insure',
  data() {
    return {
      policyNoPicker: {
        show: false,
        value: ''
      },
      policyNo: ''
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  methods: {
    handleSubmit() {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      try {
        searchByPolicyNo(this.policyNo).then((r) => {
          if (r.data.data.length === undefined) {
            this.$toast.success('校验成功')
            this.$router.push({
              name: 'SubmissionClaim',
              query: { policy_id: r.data.data.id, policy_type: r.data.data.type }
            })
          } else {
            this.$toast.fail('保单号校验失败')
          }
        })
      } catch {
        this.$toast.fail('保单号校验失败')
      }
      loading.clear()
    }
  }
}
</script>

<style lang="less" scope>
.insure-box {
  height: 100%;
  width: 100vw;
  box-sizing: border-box;
  padding: 15px;

  .square-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .item {
      h3,
      p,
      span,
      img {
        padding: 0;
        margin: 0;
      }

      flex-basis: 48%;
      background: #f5f5f5;
      text-align: center;
      box-sizing: border-box;
      padding: 11px;
      border-radius: 4px;

      .heading {
        font-size: 16px;
        color: #333333;
      }

      .link {
        display: inline-block;
        text-align: center;
        padding: 10px 0 12px 0;

        img {
          display: inline-block;
          width: 18px;
        }
      }

      .slogan {
        margin-top: 9px;
        font-size: 13px;
        color: #999999;
      }

      img {
        display: inline-block;
        box-sizing: border-box;
        padding: 0 3px 0 3px;
        width: 100%;
      }
    }
  }

  .item-wrapper {
    margin: 10px 0;
  }
  .full-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f5f5f5;
    box-sizing: border-box;

    .item {
      flex: 1;
      flex-basis: 48%;
      border-radius: 4px;
    }

    .item-img {
      box-sizing: border-box;
      padding: 10px 22px 10px 22px;
      img {
        width: 100%;
      }
    }

    .item-text {
      box-sizing: border-box;
      padding: 10px 14px 10px 14px;
      text-align: center;

      h3,
      p,
      span,
      img {
        padding: 0;
        margin: 0;
      }

      .heading {
        font-size: 16px;
        color: #333333;
      }

      .slogan {
        margin-top: 9px;
        font-size: 13px;
        color: #999;
      }

      .show-more {
        display: inline-flex;
        margin-top: 8px;
        background-color: var(--primary-color);
        padding: 3px 10px 3px 10px;
        border-radius: 4px;
        font-size: 13px;
        color: #ffffff;
      }
    }
  }
}
</style>
