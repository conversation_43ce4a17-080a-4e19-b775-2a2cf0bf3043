<template>
  <div class="tjba">
    <van-sticky>
      <van-nav-bar title="提交报案" left-arrow @click-left="$router.push({ name: 'PublicClaimHome' })" />
    </van-sticky>
    <div>
      <van-form scroll-to-error>
        <div ref="showInput">
          <div class="item-wrapper">
            <van-cell>
              <template #title>
                <img src="@/assets/imgs/insure/mode.png" alt="" />
                <span class="word">报案信息</span>
              </template>
            </van-cell>

            <!-- 出险原因 -->
            <van-field
              v-model="form.loss_reason"
              is-link
              readonly
              label="出险原因"
              class="required"
              placeholder="请选择出险原因"
              @click="reasonsPicker.show = true"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <van-popup v-model="reasonsPicker.show" round position="bottom">
              <van-cascader
                v-model="reasonsPicker.value"
                title="请选择出险原因"
                :options="reasons"
                @close="reasonsPicker.show = false"
                @finish="reasonsPickerFinish"
              />
            </van-popup>

            <!-- 出险时间 -->
            <van-field
              v-model="form.date_of_loss"
              is-link
              readonly
              label="出险时间"
              class="required"
              placeholder="请选择出险时间"
              @click="dateOfLossPicker.show = true"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <van-popup v-model="dateOfLossPicker.show" round position="bottom">
              <van-datetime-picker
                v-model="dateOfLossPicker.currentDate"
                type="datetime"
                title="选择出险时间"
                :formatter="dateOfLossFormatter"
                @cancel="dateOfLossPicker.show = false"
                @confirm="dateOfLossPickerConfirm"
              />
            </van-popup>

            <!-- 出险地 -->
            <van-field
              v-model="form.loss_location"
              is-link
              readonly
              label="出险地"
              class="required"
              placeholder="请选择出险地点"
              @click="lossLocationPicker.show = true"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <van-popup v-model="lossLocationPicker.show" round position="bottom">
              <van-search
                v-model="lossLocationSearchValue"
                placeholder="请输入搜索关键词"
                @input="lossLocationSearch"
              />
              <van-cascader
                v-model="lossLocationPicker.value"
                title="请选择出险地点"
                :options="lossLocations"
                @close="lossLocationPicker.show = false"
                @change="lossLocationPickerChange"
                @finish="lossLocationPickerFinish"
              />
            </van-popup>

            <!-- 出险详细地址 -->
            <van-field
              v-model="form.loss_address"
              rows="1"
              autosize
              type="textarea"
              label="出险详细地址"
              maxlength="100"
              placeholder="出险详细地址"
              show-word-limit
              class="none-r"
            />

            <!-- 损失类型 -->
            <van-field
              readonly
              :value="lossCategoryPicker.value"
              label="损失类型"
              placeholder="请选择损失类型"
              class="required"
              @click="lossCategoryPicker.show = true"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <van-popup v-model="lossCategoryPicker.show" position="bottom">
              <van-picker
                show-toolbar
                :columns="lossCategories"
                @confirm="confirmLossCategory"
                @cancel="lossCategoryPicker.show = false"
              />
            </van-popup>

            <!-- 报损金额 -->
            <van-field
              v-model="form.loss_amount"
              type="number"
              label="报损金额"
              placeholder="损失情况不明时请填写 1"
              class="required"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />

            <!-- 币种 -->
            <van-field
              readonly
              :value="currencyPicker.value"
              label="币种"
              placeholder="请选择币种"
              @click="currencyPicker.show = true"
              class="required"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />
            <van-popup v-model="currencyPicker.show" position="bottom">
              <van-picker
                show-toolbar
                :columns="currencies"
                @confirm="confirmCurrency"
                @cancel="currencyPicker.show = false"
              />
            </van-popup>

            <!-- 事故经过 -->
            <van-field
              v-model="form.loss_detail"
              rows="2"
              autosize
              type="textarea"
              label="事故经过"
              maxlength="2000"
              :placeholder="lossDetailPlaceholder"
              show-word-limit
              :rules="[
                { required: true, message: '此项为必填项' },
                { pattern: /^.{8,2000}$/, message: '长度在8-2000之间' }
              ]"
              class="none-r"
            />

            <!-- 报案人 -->
            <van-field
              v-model="form.claimant"
              label="报案人"
              placeholder="请输入报案人姓名"
              class="required"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />

            <!-- 报案人 -->
            <van-field
              v-model="form.claimant_email"
              label="报案人邮箱"
              placeholder="请输入报案人邮箱"
              class="required"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />

            <!-- 报案人 -->
            <van-field
              v-model="form.claimant_phone_number"
              label="报案人电话"
              placeholder="请输入报案人电话"
              class="required"
              :rules="[{ required: true, message: '此项为必填项' }]"
            />

            <van-collapse accordion v-model="contactCollapse">
              <van-collapse-item title="添加附件" name="1">
                <!-- 选择附件类别 -->
                <van-field
                  v-model="attachmentPicker.value"
                  is-link
                  readonly
                  label="文件类别"
                  placeholder="请选择文件类别"
                  @click="attachmentPicker.show = true"
                />
                <van-popup v-model="attachmentPicker.show" round position="bottom">
                  <van-cascader
                    v-model="attachmentPicker.value"
                    title="请选择文件类别"
                    :options="fileCategories"
                    @close="attachmentPicker.show = false"
                    @finish="attachmentPickerFinish"
                  />
                </van-popup>

                <!-- 添加附件 -->
                <van-field is-link readonly label="上传附件">
                  <template #button>
                    <van-uploader
                      v-model="fileList"
                      multiple
                      :preview-image="false"
                      :after-read="uploadAttachment"
                      :disabled="!attachmentPicker.value"
                      :max-size="2 * 1024 * 1024"
                    >
                      <van-button icon="plus" :color="primaryColor" size="small" :disabled="!attachmentPicker.value"
                        >上传文件</van-button
                      >
                    </van-uploader>
                  </template>
                </van-field>
                <van-uploader style="padding: 0 5%" v-if="fileList.length > 0" v-model="fileList" multiple disabled>
                  <template #preview-cover="{ file }">
                    <div class="preview-cover van-ellipsis">{{ file.text }}</div>
                  </template>
                </van-uploader>
              </van-collapse-item>
            </van-collapse>

            <!-- 其他信息-->
            <div class="item-wrapper none-r">
              <div class="btn-box">
                <van-button block :color="primaryColor" size="small" @click="handleSubmit">提交报案</van-button>
              </div>
            </div>
          </div>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
import * as claimApi from '@/apis/claim'
import chinaAreadata from '@/utils/areadata.json'
import overseaAreadata from '@/utils/regions'
import * as currencyApi from '@/apis/currency'
import * as companyApi from '@/apis/company'
import dayjs from 'dayjs'

export default {
  name: 'PublicClaimSubmission',
  data() {
    return {
      chinaAreadata,
      overseaAreadata,
      policyType: Number(this.$route.query.policy_type),
      rawCurrencies: [],
      data: {},
      product: {},
      companies: [],
      policyTypes: [
        { value: 1, text: '国内货运险' },
        { value: 2, text: '国际货运险' },
        { value: 3, text: '单车货运险' },
        { value: 5, text: '雇主责任险' },
        { value: 4, text: '其他险种' }
      ],

      form: {
        // claim info
        policy_id: this.$route.query.policy_id,
        loss_reason: '',
        date_of_loss: '',
        loss_location: '',
        loss_address: '',
        loss_category: '',
        loss_amount: '',
        loss_amount_currency_id: '',
        loss_detail: '',
        claimant: '',
        claimant_email: '',
        claimant_phone_number: ''
      },
      contactCollapse: '',

      policyTypesPicker: {
        defaultIndex: '',
        show: false,
        value: ''
      },
      companyPicker: {
        show: false,
        value: ''
      },
      companyBranchPicker: {
        show: false,
        value: ''
      },
      reasonsPicker: {
        show: false,
        value: ''
      },

      dateOfLossPicker: {
        show: false,
        currentDate: new Date()
      },
      lossLocationPicker: {
        show: false,
        index: -1,
        oldIdx: -1,
        value: ''
      },
      lossCategoryPicker: {
        show: false,
        value: ''
      },
      coverageCurrencyPicker: {
        show: false,
        value: ''
      },
      currencyPicker: {
        show: false,
        value: ''
      },

      attachmentPicker: {
        show: false,
        value: '',
        data: []
      },
      fileList: [],
      lossLocations: [],
      lossLocationSearchValue: ''
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    },
    isDomestic() {
      return this.policyType !== 2 && this.policyType !== ''
    },
    currencies() {
      if (this.isDomestic) {
        return [{ id: -1, text: '人民币', value: 'CNY' }]
      }

      return this.rawCurrencies
    },
    fileCategories() {
      switch (this.policyType) {
        case 1:
        case 2:
        case 3:
          return [
            {
              text: '运输单证',
              value: '运输单证',
              children: [
                { text: '提单', value: '提单' },
                { text: '运输合同/运单', value: '运输合同/运单' },
                { text: '装车清单', value: '装车清单' },
                { text: '运输经营许可证', value: '运输经营许可证' },
                { text: '驾驶证', value: '驾驶证' },
                { text: '行驶证', value: '行驶证' },
                { text: '车辆道路运输证', value: '车辆道路运输证' },
                { text: '监装报告', value: '监装报告' },
                { text: '设备交接单EIR', value: '设备交接单EIR' },
                { text: '签收交接单', value: '签收交接单' }
              ]
            },
            {
              text: '货物单证',
              value: '货物单证',
              children: [
                { text: '购货合同', value: '购货合同' },
                { text: '形式发票', value: '形式发票' },
                { text: '装箱单', value: '装箱单' },
                { text: '报关单', value: '报关单' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '现场照片视频类',
              value: '现场照片视频类',
              children: [
                {
                  text: '运输工具（全貌、船舱、车厢的密封情况、受损状况、船名、车牌号）',
                  value: '运输工具（全貌、船舱、车厢的密封情况、受损状况、船名、车牌号）'
                },
                {
                  text: '集装箱（全貌、集装箱号、内外状况、受损细节）',
                  value: '集装箱（全貌、集装箱号、内外状况、受损细节）'
                },
                {
                  text: '标的货物（全貌、包装、运输标志、防震动、防倾、温控标签、绑扎和积载、铭牌、受损细节）',
                  value: '标的货物（全貌、包装、运输标志、防震动、防倾、温控标签、绑扎和积载、铭牌、受损细节）'
                },
                { text: '发货现场（装箱情况、标的物状态）', value: '发货现场（装箱情况、标的物状态）' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定责类',
              value: '定责类',
              children: [
                { text: '交通责任认定书', value: '交通责任认定书' },
                { text: '火灾证明', value: '火灾证明' },
                { text: '破损单', value: '破损单' },
                { text: '理货报告', value: '理货报告' },
                { text: '承运人出具的事故说明', value: '承运人出具的事故说明' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定损类',
              value: '定损类',
              children: [
                { text: '报损清单', value: '报损清单' },
                { text: '核验报告', value: '核验报告' },
                { text: '鉴定报告', value: '鉴定报告' },
                { text: '其他', value: '其他' }
              ]
            }
          ]
        // 其他
        case 4:
          return [
            {
              text: '定责类',
              value: '定责类',
              children: [
                { text: '火灾证明', value: '火灾证明' },
                { text: '交通事故责任认定书', value: '交通事故责任认定书' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定损类',
              value: '定损类',
              children: [
                { text: '报损明细', value: '报损明细' },
                { text: '损失证明', value: '损失证明' },
                { text: '检查报告', value: '检查报告' },
                { text: '维修报价', value: '维修报价' },
                { text: '其他', value: '其他' }
              ]
            }
          ]
        // 雇主
        case 5:
          return [
            {
              text: '定责类',
              value: '定责类',
              children: [
                { text: '受伤员工劳动合同（注明工种）', value: '受伤员工劳动合同（注明工种）' },
                { text: '病历、检验报告、出院小结', value: '病历、检验报告、出院小结' },
                { text: '工伤认定书、伤残鉴定书、死亡证明', value: '工伤认定书、伤残鉴定书、死亡证明' },
                { text: '交通责任认定书、交通事故调解书', value: '交通责任认定书、交通事故调解书' },
                { text: '事故情况说明', value: '事故情况说明' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '定损类',
              value: '定损类',
              children: [
                { text: '医疗费用明细', value: '医疗费用明细' },
                { text: '医疗费用发票/收据', value: '医疗费用发票/收据' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '现场照片视频类',
              value: '现场照片视频类',
              children: [
                { text: '事故现场', value: '事故现场' },
                { text: '伤者情况', value: '伤者情况' },
                { text: '其他', value: '其他' }
              ]
            }
          ]
        default:
          return []
      }
    },
    locations() {
      if (this.isDomestic) {
        return chinaAreadata.map((item) => {
          return {
            text: item.value,
            value: item.value,
            children: item.city.map((city) => {
              return {
                text: city.value,
                value: city.value
              }
            })
          }
        })
      }

      return [
        {
          text: '中国大陆',
          value: '中国大陆',
          children: chinaAreadata.map((item) => {
            return {
              text: item.value,
              value: item.value,
              children: item.city.map((city) => {
                return {
                  text: city.value,
                  value: city.value
                }
              })
            }
          })
        },
        {
          text: '境外地区',
          value: '境外地区',
          children: Object.keys(this.overseaAreadata).map((k) => {
            return { text: k, value: k }
          })
        }
      ]
    },
    reasons() {
      // 货运险
      switch (this.policyType) {
        case 1:
        case 2:
        case 3:
          return [
            {
              text: '自然灾害',
              value: '自然灾害',
              children: [
                { text: '恶劣气候', value: '恶劣气候' },
                { text: '雷电', value: '雷电' },
                { text: '海啸', value: '海啸' },
                { text: '地震', value: '地震' },
                { text: '洪水', value: '洪水' },
                { text: '暴风', value: '暴风' },
                { text: '暴雨', value: '暴雨' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '意外事故',
              value: '意外事故',
              children: [
                { text: '运输工具意外', value: '运输工具意外' },
                { text: '火灾', value: '火灾' },
                { text: '爆炸', value: '爆炸' },
                { text: '装卸货意外', value: '装卸货意外' },
                { text: '震动碰撞挤压', value: '震动碰撞挤压' },
                { text: '水湿', value: '水湿' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '共同海损',
              value: '共同海损',
              children: [
                { text: '共同海损牺牲分摊', value: '共同海损牺牲分摊' },
                { text: '救助费用分摊', value: '救助费用分摊' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '其他',
              value: '其他',
              children: [{ text: '货物丢失', value: '货物丢失' }]
            }
          ]
        case 4:
          return [
            {
              text: '自然灾害',
              value: '自然灾害',
              children: [
                { text: '恶劣气候', value: '恶劣气候' },
                { text: '雷电', value: '雷电' },
                { text: '海啸', value: '海啸' },
                { text: '地震', value: '地震' },
                { text: '洪水', value: '洪水' },
                { text: '暴风', value: '暴风' },
                { text: '暴雨', value: '暴雨' },
                { text: '其他', value: '其他' }
              ]
            },
            {
              text: '意外事故',
              value: '意外事故',
              children: [
                { text: '交通事故', value: '交通事故' },
                { text: '火灾', value: '火灾' },
                { text: '爆炸', value: '爆炸' },
                { text: '其他', value: '其他' }
              ]
            }
          ]
        case 5:
          return [
            {
              text: '受伤',
              value: '受伤',
              children: [
                { text: '因工作原因受到事故伤害', value: '因工作原因受到事故伤害' },
                { text: '因履行工作职责受到暴力等意外伤害', value: '因履行工作职责受到暴力等意外伤害' },
                { text: '工作原因受到伤害或者发生事故下落不明', value: '工作原因受到伤害或者发生事故下落不明' },
                {
                  text: '上下班途中，受到非本人主要责任的交通事故伤害',
                  value: '上下班途中，受到非本人主要责任的交通事故伤害'
                },
                { text: '其他原因受伤', value: '其他原因受伤' }
              ]
            },
            {
              text: '死亡',
              value: '死亡',
              children: [
                { text: '工作期间突发疾病死亡', value: '工作期间突发疾病死亡' },
                { text: '在48小时之内经抢救无效死亡', value: '在48小时之内经抢救无效死亡' },
                { text: '其他原因受伤', value: '其他原因受伤' }
              ]
            },
            {
              text: '其他',
              value: '其他',
              children: [{ text: '其他', value: '其他' }]
            }
          ]
        default:
          return []
      }
    },
    lossCategories() {
      switch (this.policyType) {
        case 1:
        case 2:
        case 3:
          return [{ text: '物损', value: 2 }]
        case 5:
          return [{ text: '人伤', value: 1 }]
        default:
          return [
            { text: '人伤', value: 1 },
            { text: '物损', value: 2 },
            { text: '人伤和物损', value: 3 }
          ]
      }
    },
    lossDetailPlaceholder() {
      if ([1, 2, 3].includes(this.policyType)) {
        return 'XYZ公司从上海出口标的货物空气开关3箱到英国伦敦ABC客户，委托DDFF承运，运单号为DDFF111111，运输方式为航空运输。2023年1月1日，在伦敦卸离飞机后陆运到买家仓库途中发生交通事故，经初步清点，事故造成10只空气开关受损。'
      }

      if (this.policyType === 5) {
        return '2023年9月1日10：00时左右，上海某机械有限公司员工张某在车床加工零件作业时，车床在无指令的状况下突然启动，导致张某右手受伤，事发后立即送往上海第六人民医院治疗，经诊断为手指骨折，在手术治疗中。'
      }

      return '请填写事故经过'
    }
  },
  watch: {
    policyType(policyType) {
      // 雇主默认人伤
      if (policyType === 5) {
        this.form.loss_category = 1
      } else if ([1, 2, 3].includes(policyType)) {
        this.form.loss_category = 2
      }
    },
    'form.company_id'(value, oldValue) {
      if (oldValue !== value) {
        this.form.company_branch_id = ''
        this.companyBranchPicker.value = ''
      }
    }
  },
  async created() {
    await this.fetchCompanies()
    await this.fetchCurrencies()
    this.lossLocations = this.locations
  },
  methods: {
    reasonsPickerFinish(data) {
      this.reasonsPicker.show = false
      this.form.loss_reason = data.selectedOptions.map((option) => option.text).join('/')
    },
    dateOfLossFormatter(type, val) {
      if (type === 'year') {
        return `${val}年`
      } else if (type === 'month') {
        return `${val}月`
      } else if (type === 'day') {
        return `${val}日`
      } else if (type === 'hour') {
        return `${val}时`
      } else if (type === 'minute') {
        return `${val}分`
      }
      return val
    },
    dateOfLossPickerConfirm(value) {
      this.dateOfLossPicker.show = false
      this.form.date_of_loss = dayjs(value).format('YYYY-MM-DD HH:mm:ss')
    },
    lossLocationPickerFinish(data) {
      this.lossLocationPicker.show = false
      this.form.loss_location = data.selectedOptions.map((option) => option.text).join('/')
      this.lossLocationPicker.index = -1
      this.lossLocationPicker.oldIdx = -1
      this.lossLocationSearchValue = ''
      this.lossLocations = this.locations
    },
    lossLocationPickerChange(val) {
      this.lossLocationSearchValue = ''
      this.lossLocationPicker.oldIdx = this.lossLocationPicker.index
      this.lossLocationPicker.index = val.tabIndex
      if (!(this.lossLocationPicker.oldIdx < this.lossLocationPicker.index)) {
        this.lossLocations = this.locations
      }
    },
    lossLocationSearch(value) {
      if (this.lossLocationPicker.index === -1) {
        this.lossLocations = this.locations.filter((item) => {
          return item.text.includes(value)
        })
      } else if (this.lossLocationPicker.index === 0) {
        this.lossLocations = this.locations.map((item) => {
          return {
            text: item.value,
            value: item.value,
            children: item.children.filter((item1) => {
              return item1.text.includes(value)
            })
          }
        })
      } else if (this.lossLocationPicker.index === 1) {
        this.lossLocations = this.locations.map((item) => {
          return {
            text: item.value,
            value: item.value,
            children: item.children.map((item1) => {
              return {
                text: item1.value,
                value: item1.value,
                children: item1?.children?.filter((item2) => {
                  return item2.text.includes(value)
                })
              }
            })
          }
        })
      }
    },
    confirmLossCategory(value) {
      this.lossCategoryPicker.show = false
      if (value) {
        this.lossCategoryPicker.value = value.text
        this.form.loss_category = value.value
      }
    },
    confirmCurrency(value) {
      this.currencyPicker.show = false
      if (value) {
        this.currencyPicker.value = value.text
        this.form.loss_amount_currency_id = value.id
      }
    },
    attachmentPickerFinish(data) {
      this.attachmentPicker.show = false
      this.attachmentPicker.data = []
      this.attachmentPicker.value = data.selectedOptions.map((option) => option.text).join('/')
      data.selectedOptions.map((option) => this.attachmentPicker.data.push(option.text))
    },
    uploadAttachment(file) {
      file.name = file.file.name
      file.file.text = this.attachmentPicker.value + '-' + file.name
      file.category = {
        primary: this.attachmentPicker.data[0],
        secondary: this.attachmentPicker.data[1]
      }
    },
    async fetchCurrencies() {
      const currencies = await currencyApi.getPublicCurrencies()

      this.rawCurrencies = currencies.data.data.map((item) => {
        return {
          id: item.id,
          text: item.name,
          value: item.code
        }
      })
    },
    async fetchCompanies() {
      const companies = await companyApi.getPublicCompaniesDict()
      this.companies = companies.data.data
    },
    async handleSubmit() {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      try {
        const data = this.form
        const caseData = await claimApi.publicCreateClaim(data)
        for (const f of this.fileList) {
          await claimApi.publicUploadAttachment(caseData.data.data.id, {
            primary_category: f.category.primary,
            secondary_category: f.category.secondary,
            name: f.name,
            file: f.file
          })
        }
        this.$toast.success('报案成功')
        this.$router.push({ name: 'PublicClaimProgressDetail', params: { id: caseData.data.data.id } })
      } catch {
        this.$toast.fail('报案失败')
      }
      loading.clear()
    }
  }
}
</script>

<style lang="less" scoped>
.tjba {
  background: #ededed;
  min-height: 100vh;
  background-color: #ededed;
  /deep/ .van-nav-bar__text,
  /deep/ .van-icon-arrow-left {
    color: #333;
  }
  .item-wrapper {
    background-color: #fff;
    margin-top: 10px;
    padding-bottom: 30px;
  }
  .preview-cover {
    position: absolute;
    bottom: 0;
    box-sizing: border-box;
    width: 100%;
    padding: 4px;
    color: #fff;
    font-size: 12px;
    text-align: center;

    background: rgba(0, 0, 0, 0.3);
  }
}
// 必填项
.required::after {
  content: '*' !important;
  color: red;
  left: 3.5rem;
  font-size: 18px;
  bottom: auto;
  transform: none;
}

/deep/ .none-r {
  .van-cell__title.van-field__label {
    span {
      &::after {
        content: none !important;
      }
    }
  }
}
// 保单填写
.van-cell {
  img {
    width: 12px;
    // height: 15px;
    background-size: contain;
    line-height: 15px;
  }
  .word {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-left: 5px;
  }
}
// 左侧内容label宽度
/deep/ .van-cell__title.van-field__label {
  width: 120px;
}
// 上传文件位置
/deep/ .van-field__control.van-field__control--custom {
  justify-content: flex-end;
}
.word_color {
  color: var(--primary-color);
}
.van-checkbox {
  font-size: 14px;
  color: #333;
}
.btn-box {
  padding: 10px 15px;
  .van-button {
    margin-bottom: 10px;
  }
}
/deep/ .van-cell--borderless::after,
.van-cell:last-child::after {
  display: block;
  border-bottom: none;
}
</style>
