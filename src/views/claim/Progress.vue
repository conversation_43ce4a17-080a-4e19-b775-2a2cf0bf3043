<template>
  <div class="claim">
    <van-sticky>
      <van-search v-model="value" placeholder="保单号/理赔编号/保司报案号" @input="inputContent" />
    </van-sticky>
    <div class="wrapper">
      <div class="card">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多啦" @load="onLoad">
          <van-cell-group v-for="item in data" :key="item.id" @click="goToDetail(item)">
            <van-cell>
              <template #title>
                <span style="font-size: 18px; font-weight: bold; color: #333">
                  {{ item.case_no }}
                </span>
              </template>
            </van-cell>
            <van-field class="van-mini-space" :placeholder="getPolicyType(item.policy_type)" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">险种</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.policy_no" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">保单号</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.external_case_no" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">保司报案号</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.policyholder" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">投保人</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.insured" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">被保险人</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.created_at" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">报案时间</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.duration + '天'" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">报案时长</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.subject" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">标的</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item.status_text" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">理赔进度</span>
              </template>
            </van-field>

            <van-field
              class="van-mini-space"
              :placeholder="item.settlement_payment_amount + ' (' + item.settlement_payment_amount_currency + ')'"
              disabled
            >
              <template #label>
                <span style="font-size: 14px; color: #999">获赔金额</span>
              </template>
            </van-field>

            <van-field class="van-mini-space" :placeholder="item?.operator?.name" disabled>
              <template #label>
                <span style="font-size: 14px; color: #999">理赔员</span>
              </template>
            </van-field>

            <div class="clickBtn">
              <van-button
                v-if="![0, 4, 5, 7].includes(item.status) && !item.hurry_up_at"
                size="small"
                plain
                round
                color="#999"
                @click.stop="hurryUp(item)"
              >
                催办
              </van-button>
              <van-button
                v-if="![0, 4, 5, 7].includes(item.status) && item.hurry_up_at"
                size="small"
                plain
                round
                color="#999"
                disabled
              >
                已催办
              </van-button>
              <van-button size="small" plain round color="#999" @click.stop="goToDetail(item)"> 详情 </van-button>
            </div>
          </van-cell-group>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script>
import * as claimApi from '@/apis/claim'

export default {
  name: 'Claimable',
  data() {
    return {
      // 1:国内 2:国际 3:单车 4:通用 5:雇主
      title: [
        { type: 1, name: '国内货运险' },
        { type: 2, name: '国际货运险' },
        { type: 5, name: '雇主责任险' },
        { type: 3, name: '单车责任险' },
        { type: 4, name: '其他险种' },
        { type: -1, name: '' }
      ],
      // active: 1,
      tableType: '全部保单',
      selectValue: undefined,
      showPicker: false,
      columns: ['全部保单', '已提交', '已出单'],
      value: '',
      is_four: false,
      is_three: false,
      newName: 0,
      data: [],
      loading: false,
      finished: false,
      paging: {
        current: 0,
        total: 0
      }
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  methods: {
    onLoad() {
      this.paging.current++
      this.getList({
        filter: { series_no: this.value },
        page: this.paging.current
      })
    },
    getList(data) {
      claimApi.fetchSubmittedClaims(data).then((r) => {
        this.data.push(...r.data.data)
        this.loading = false
        this.paging.current = r.data.meta.current_page
        this.paging.total = r.data.meta.total
        this.paging.last_page = r.data.meta.last_page

        if (this.paging.current >= r.data.meta.last_page) {
          this.finished = true
        }
      })
    },
    inputContent() {
      this.data = []
      this.finished = false
      this.paging = {
        current: 1,
        total: 0
      }
      this.getList({
        filter: { series_no: this.value },
        page: this.paging.current
      })
    },
    changeList() {
      this.showPicker = true
    },
    /* eslint-disable */
    goToDetail(item) {
      this.$router.push({ name: 'ClaimProgressDetail', params: { id: item.id } })
    },
    async hurryUp(item) {
      const loading = this.$toast.loading({ forbidClick: true, duration: 0 })
      try {
        await claimApi.hurryUp(item.id)
        this.$toast.success('催办成功')
        this.value = ''
        this.inputContent()
      } catch {
        this.$toast.fail('催办失败')
      }
      loading.clear()
    },
    getPolicyType(type) {
      const types = {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任险',
        4: '其他险种',
        5: '雇主责任险'
      }

      return types[type]
    }
  }
}
</script>

<style lang="less" scoped>
.claim {
  padding: 0 0 60px;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #fafafa;

  /deep/ .van-sticky {
    background-color: #fff;
    box-shadow: 2px 2px 10px 0px rgba(0, 0, 0, 0.05) !important;
    .search-item {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      margin-bottom: 15px;
      .item {
        box-sizing: border-box;
        text-align: center;
        height: 40px;
        line-height: 80px;
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
      .active {
        color: var(--primary-color);
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
  .wrapper {
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    p {
      margin: 0;
      margin: 16px 0;
      display: flex;
      justify-content: space-between;
      .total {
        color: #999;
        font-size: 14px;
      }
      .dropdown {
        color: #333;
        font-size: 14px;
        position: relative;
        margin-right: 16px;
        /deep/ .van-icon {
          position: absolute;
          top: 1px;
          // transform: rotate(90deg);
        }
      }
    }
    .card {
      box-shadow: 4px 4px 20px 0px rgba(0, 0, 0, 0.05);
      border-radius: 20px;
    }
  }
}

.van-cell-group {
  margin-top: 16px;
}
// 箭头方向
.arrowStatus {
  transform: rotate(-90deg);
  padding: 0 6px;
  line-height: 12px;
  height: 12px;
}
// 多余线
.van-cell-group .van-cell:not(:first-child):after {
  border-bottom: none !important;
}
.clickBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  .van-button {
    margin-right: 14px;
    margin-bottom: 16px;
    width: 85px;
    height: 30px;
    font-size: 14px;
    white-space: nowrap;
  }
}
.status-style {
  color: #07c160;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #07c160;
    margin-right: 6px;
  }
}
.drogdown {
  display: block;
}
//
/deep/ .van-tabs__wrap {
  overflow: visible;
  .van-tab {
    padding: 0 7px;
  }
}
// 已退回、未提交
.color1 {
  color: #d0021b;
  &::before {
    background-color: #d0021b;
  }
}
//已审核、已出单、已支付
.color2 {
  color: #5cc374;
  &::before {
    background-color: #5cc374;
  }
}
//已作废、已退保
.color3 {
  color: #979797;
  &::before {
    background-color: #979797;
  }
}
//审核中、退保中、批改中
.color4 {
  color: #fb7203;
  &::before {
    background-color: #fb7203;
  }
}
//已提交、待确认、待支付、暂存单
.color5 {
  color: #f29925;
  &::before {
    background-color: #f29925;
  }
}

.van-mini-space {
  padding: 5px 15px !important;
}
</style>
