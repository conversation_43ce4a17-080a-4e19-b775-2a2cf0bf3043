<template>
  <div class="login">
    <div class="bg-top">
      <div class="img-box">
        <img :src="platform.logo_white" alt="" />
      </div>
    </div>
    <div class="bg-bottom">
      <p>{{ platform.description }}</p>
    </div>
    <div class="content-box">
      <h1>{{ platform.slogan }}</h1>
      <van-search left-icon="manager" v-model="loginData.username" placeholder="请输入用户名" />
      <van-search
        left-icon="lock"
        v-model="loginData.password"
        placeholder="请输入密码"
        type="password"
        style="margin-top: 8px; padding-top: 0px"
      />
      <van-row class="row-s">
        <van-col :span="16">
          <van-search
            left-icon="bell"
            v-model="loginData.captcha"
            placeholder="验证码"
            style="margin-top: 8px; padding-top: 0px"
          />
        </van-col>
        <van-col :span="8" style="padding-top: 10px">
          <van-image height="100%" :src="captchaSrc" @click="fetchCaptchaSrc" />
        </van-col>
      </van-row>
      <van-row class="register">
        <van-col :span="12">
          <span @click="goToRegisterPage">申请开户</span>
        </van-col>
        <van-col :span="12">
          <span @click="$router.push({ name: 'forgot-password' })">忘记密码？</span>
        </van-col>
      </van-row>
      <div>
        <van-button type="primary" size="large" :color="primaryColor" @click="goToHome">登录</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { login, captchaSrc, getUser } from '@/apis/user'
import { findByDomain } from '@/apis/platform'
import { tokenKey, userKey } from '@/config'

export default {
  name: 'Login',
  data() {
    return {
      captchaSrc: '',
      loginData: {
        username: '',
        password: '',
        captcha_key: '',
        captcha: ''
      },
      platform: {}
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  created() {
    this.fetchCaptchaSrc()

    findByDomain(window.location.hostname).then((r) => {
      this.platform = r.data.data
    })
  },
  methods: {
    fetchCaptchaSrc() {
      captchaSrc().then((r) => {
        this.captchaSrc = r.data.data.img
        this.loginData.captcha_key = r.data.data.key
      })
    },
    // 跳转到注册页
    goToRegisterPage() {
      this.$router.push({
        name: 'register'
      })
    },
    // 页面登录
    goToHome() {
      const _data = Object.assign({}, this.loginData)
      login(_data)
        .then((res) => {
          localStorage.setItem(tokenKey, res.data.access_token)

          getUser().then((r) => {
            localStorage.setItem(userKey, JSON.stringify(r.data.data))
          })
          this.$router.push({
            name: 'home'
          })
        })
        .catch((err) => {
          if (err.response?.data?.message?.captcha?.indexOf('验证码') !== -1) {
            this.fetchCaptchaSrc()
          }
        })
    }
  }
}
</script>

<style lang="less" scoped>
.login {
  min-height: 100vh;
  box-sizing: border-box;
  position: relative;
  .bg-top {
    height: 248px;
    background-color: var(--primary-color);
    position: relative;
    .img-box {
      width: 100%;
      box-sizing: border-box;

      padding-left: 10%;
      padding-right: 10%;
      padding-top: 15%;
      text-align: center;

      img {
        max-width: 100%;
        max-height: 2rem;
      }
    }
  }
  .bg-bottom {
    background-color: #fff;
    p {
      width: 100%;
      color: #333;
      font-size: 10px;
      position: absolute;
      bottom: 20px;
      text-align: center;
    }
  }
  .content-box {
    height: 338px;
    background-color: red;
    display: block;
    position: absolute;
    top: 170px;
    left: 23px;
    right: 23px;
    background: #ffffff;
    box-shadow: 0px 5px 22px 0px rgba(104, 104, 104, 0.1);
    border-radius: 10px;
    h1 {
      font-size: 20px;
      color: #333;
      text-align: center;
      margin-top: 24px;
    }
  }
}
// 搜索框样式
.van-search__content.van-search__content--square {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  height: 41px;
  background-color: #fff;
}
/deep/ .van-cell__value.van-cell__value--alone.van-field__value {
  line-height: 31px;
}

/deep/ .van-field__control {
  padding-left: 23px;
}
/deep/ .van-field__left-icon {
  font-size: 14px;
  line-height: 31px;
  color: #dedede;
}
.van-search {
  margin-left: 25px;
  margin-right: 25px;
  padding-left: 0px;
  padding-right: 0px;
}
.yzm {
  box-sizing: border-box;
  margin-top: 8px;
  text-align: center;
  line-height: 40px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  color: #999999;
  font-size: 23px;
}
.row-s {
  padding-right: 30px;
}
.register {
  padding: 0 26px;
  span {
    display: block;
    color: var(--primary-color);
    font-size: 12px;
    margin-top: 10px;
  }
  .van-col:last-child {
    span {
      text-align: right;
    }
  }
}
.van-button {
  display: block;
  width: 280px;
  height: 40px;
  margin: 12px auto;
}
</style>
