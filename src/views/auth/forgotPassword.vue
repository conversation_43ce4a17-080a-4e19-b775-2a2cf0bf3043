<template>
  <div class="login">
    <div class="bg-top">
      <div class="img-box">
        <img :src="platform.logo_white" alt="" />
      </div>
    </div>
    <div class="bg-bottom">
      <p>{{ platform.description }}</p>
    </div>
    <div class="content-box">
      <h1>申请开户</h1>
      <van-search
        left-icon="coupon"
        v-model="form.email"
        placeholder="请输入邮箱"
        style="margin-top: 8px; padding-top: 0px"
      />
      <div class="verify-code">
        <div>
          <van-search
            left-icon="bell"
            v-model="form.captcha"
            placeholder="验证码"
            style="margin-top: 8px; padding-top: 0px"
          />
        </div>
        <div class="yzm" @click="sendYzm">{{ yzmWord }}</div>
      </div>
      <van-search
        left-icon="lock"
        type="password"
        v-model="form.password"
        placeholder="请输入密码"
        style="margin-top: 8px; padding-top: 0px"
      />
      <van-search
        left-icon="lock"
        type="password"
        v-model="form.password_confirmation"
        placeholder="请确认密码"
        style="margin-top: 8px; padding-top: 0px"
      />
      <div>
        <van-button type="primary" size="large" :color="primaryColor" @click="handleSubmit">重置密码</van-button>
        <div class="loginBtn">没有账号？<span @click="$router.push({ name: 'register' })">申请开户</span></div>
      </div>
    </div>
  </div>
</template>

<script>
import { sendForgotPasswordCaptcha, resetPassword } from '@/apis/user'
import { findByDomain } from '@/apis/platform'

export default {
  name: 'register',
  data() {
    return {
      count: 0,
      yzmWord: '发送验证码',
      form: {
        email: '',
        captcha: '',
        password: '',
        password_confirmation: ''
      },
      platform: {}
    }
  },
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  created() {
    findByDomain(window.location.hostname).then((r) => {
      this.platform = r.data.data
    })
  },
  methods: {
    // 发送验证码
    sendYzm() {
      if (!this.form.email) {
        this.$notify({
          type: 'warning',
          message: '请输入邮箱'
        })
        return
      }
      if (this.count) {
        this.$notify({
          type: 'warning',
          message: `请${this.count}s后再试`
        })
        return
      }
      sendForgotPasswordCaptcha({
        email: this.form.email
      }).then((res) => {
        this.count = 120
        const timer = setInterval(() => {
          --this.count
          this.yzmWord = this.count
          if (!this.count) {
            clearInterval(timer)
          }
        }, 1000)
      })
    },
    // 注册
    handleSubmit() {
      const _keys = Object.keys(this.form)
      let hasData = true
      _keys.forEach((item) => {
        if (this.form[item] === '') {
          hasData = false
          this.$notify({
            type: 'warning',
            message: `${item}不能为空`
          })
        }
      })
      if (!hasData) {
        return
      }
      const resetPasswordData = Object.assign({}, this.form)
      resetPassword(resetPasswordData).then((res) => {
        this.$notify({
          type: 'success',
          message: '密码重置成功，请重新登录'
        })
        this.$router.push({
          name: 'login'
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.login {
  min-height: 100vh;
  box-sizing: border-box;
  position: relative;
  .bg-top {
    height: 248px;
    background-color: var(--primary-color);
    position: relative;
    .img-box {
      width: 100%;
      box-sizing: border-box;

      padding-left: 10%;
      padding-right: 10%;
      padding-top: 15%;
      text-align: center;

      img {
        max-width: 100%;
        max-height: 2rem;
      }
    }
  }
  .bg-bottom {
    background-color: #fff;
    p {
      width: 100%;
      color: #333;
      font-size: 10px;
      position: absolute;
      bottom: 20px;
      text-align: center;
    }
  }
  .content-box {
    height: 400px;
    background-color: red;
    display: block;
    position: absolute;
    top: 150px;
    left: 23px;
    right: 23px;
    background: #ffffff;
    box-shadow: 0px 5px 22px 0px rgba(104, 104, 104, 0.1);
    border-radius: 10px;
    h1 {
      font-size: 20px;
      color: #333;
      text-align: center;
      margin-top: 24px;
    }
  }
}
// 搜索框样式
.van-search__content.van-search__content--square {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  height: 41px;
  background-color: #fff;
}
/deep/ .van-cell__value.van-cell__value--alone.van-field__value {
  line-height: 31px;
}
/deep/ .van-field__left-icon {
  font-size: 14px;
  line-height: 31px;
  // // &::before {
  color: #dedede;
  // // }
}
/deep/ .van-field__control {
  padding-left: 23px;
}

.van-search {
  margin-left: 25px;
  margin-right: 25px;
  padding-left: 0px;
  padding-right: 0px;
}

.verify-code {
  display: flex;
  align-content: center;
  align-self: center;
  align-items: center;
  margin-right: 26px;

  & > div:first-child {
    flex: 2;
  }

  .yzm {
    margin-top: -2px;
    width: 120px !important;
    box-sizing: border-box;
    text-align: center;
    line-height: 40px;
    border-radius: 4px;
    color: var(--primary-color);
    font-size: 13px;
    border: 1px solid var(--primary-color);
  }
}
.register {
  padding: 0 26px;
  span {
    display: block;
    color: var(--primary-color);
    font-size: 12px;
    margin-top: 10px;
  }
  .van-col:last-child {
    span {
      text-align: right;
    }
  }
}
.van-button {
  display: block;
  width: 280px;
  height: 40px;
  margin: 12px auto;
}
.loginBtn {
  color: #333;
  font-size: 12px;
  text-align: center;
  span {
    color: var(--primary-color);
  }
}
</style>
