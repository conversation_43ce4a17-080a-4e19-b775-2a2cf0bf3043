<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2022-01-17 10:42:35
 * @LastEditors: yanb
 * @LastEditTime: 2023-11-02 17:12:52
-->
<template>
  <div class="app-layout">
    <div class="app-layout__view">
      <router-view></router-view>
    </div>
    <van-tabbar v-model="active" :active-color="primaryColor" route>
      <van-tabbar-item replace name="home" icon="wap-home-o" to="/home">主页</van-tabbar-item>
      <van-tabbar-item replace name="insure" icon="records" to="/insure" v-if="features?.includes('insure')">
        在线投保
      </van-tabbar-item>
      <van-tabbar-item
        replace
        name="manage"
        icon="description"
        :to="{ name: 'Policies' }"
        v-if="features?.includes('insure')"
      >
        保单管理
      </van-tabbar-item>
      <van-tabbar-item replace name="claimable" icon="phone" to="/claimable" v-if="features?.includes('claims')">
        我要报案
      </van-tabbar-item>
      <van-tabbar-item
        replace
        name="claim-progress"
        icon="service-o"
        v-if="features?.includes('claims')"
        :to="{ name: 'ClaimProgress' }"
        >理赔进度</van-tabbar-item
      >
      <van-tabbar-item replace name="my" icon="contact" to="/my">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Index',
  computed: {
    ...mapGetters(['features', 'primaryColor'])
  },
  data() {
    return {
      active: 'home'
    }
  }
}
</script>

<style lang="less" scoped>
.app-layout {
  display: flex;
  flex-direction: column;

  .app-layout__view {
    height: calc(100vh - 50px);
  }
}
</style>
