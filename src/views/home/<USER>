.home-page {
  height: 100%;
  overflow-y: auto;
  background: #ededed;
  .home-header {
    position: relative;
    margin: 0;
    padding: 25px 15px;
    background: #fff;
    &::before {
      content: '';
      position: absolute;
      height: 126px;
      left: -15px;
      top: 0;
      right: 0;
      background: url('../../assets/imgs/home/<USER>') no-repeat;
      background-size: cover;
    }
    & > * {
      position: relative;
      z-index: 1;
    }
    .home-header__title {
      color: #fff;
      font-size: 18px;
      padding: 25px 0;
      margin: 0;
      font-weight: bold;
    }
    .home-header__statistics {
      font-size: 12px;
      color: #999;
      display: flex;
      gap: 10px;
      padding: 25px 0;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 0 15px 4px rgba(212, 212, 212, 0.5);
      .home-header__statistics-item {
        display: flex;
        flex-direction: column;
        flex: 1;
        text-align: center;
        line-height: 20px;
        strong {
          font-size: 16px;
          color: #333;
        }
        & + .home-header__statistics-item {
          border-left: 1px solid #eee;
        }
      }
    }

    .home-header__shortcuts {
      display: flex;
      gap: 10px;
      .home-header__shortcuts-item {
        display: flex;
        flex-direction: column;
        flex: 1;
        text-align: center;
        margin-bottom: 25px;
        .van-image {
          margin: 25px auto 10px;
        }
      }
    }
  }
  .van-notice-bar {
    margin: 10px 0;
  }
  .home-main {
    background: #fff;
    padding: 0 15px 15px;
    box-sizing: border-box;
    .home-main__title {
      padding: 20px 0;
      font-size: 18px;
    }
    .home-main__products {
      list-style: none;
      padding: 0;
      .home-main__products-item {
        display: flex;
        .van-image {
          min-width: 100px;
        }
        .product-content {
          flex: 1;
          box-sizing: border-box;
          overflow: hidden;
          padding: 0 15px 0 10px;
          .product-content__title {
            margin: 0;
            font-size: 16px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
          .product-content__desc {
            margin: 5px 0;
            font-size: 14px;
            color: #999;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            p {
              margin: 0;
            }
          }
          .product-content__tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            .van-tag {
              padding: 3px 5px;
            }
          }
          .product-content__footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 5px;
            .footer-price {
              color: #ff3c3c;
              label {
                font-size: 18px;
                font-weight: bold;
              }
            }
            .footer-prodect {
              color: #999;
            }
          }
        }
      }
    }
  }
}
