<template>
  <section class="home-page">
    <header :data-bg="headerBg" class="home-header">
      <h1 class="home-header__title" @click="handleClickUsername">欢迎使用、{{ pageData.user.name }}</h1>
      <div class="home-header__statistics">
        <div class="home-header__statistics-item">
          <strong>￥{{ pageData.user.balance }}</strong>
          <span>余额</span>
        </div>
        <div class="home-header__statistics-item" @click="$router.push({ name: 'Policies' })">
          <strong>{{ pageData.policy.count }}</strong>
          <span>保单数</span>
        </div>
        <div class="home-header__statistics-item" @click="$router.push({ name: 'Messages' })">
          <strong>{{ pageData.messages.length }}</strong>
          <span>消息</span>
        </div>
      </div>
      <div class="home-header__shortcuts">
        <div
          class="home-header__shortcuts-item"
          v-if="features?.includes('insure.domestic')"
          @click="$router.push({ name: 'InsureDomestic' })"
        >
          <van-image width="40" height="40" :src="require('@/assets/imgs/home/<USER>')" />
          <span>国内货运险</span>
        </div>
        <div
          class="home-header__shortcuts-item"
          v-if="features?.includes('insure.intl')"
          @click="$router.push({ name: 'InsureInternational' })"
        >
          <van-image width="40" height="40" :src="require('@/assets/imgs/home/<USER>')" />
          <span>国际货运险</span>
        </div>
        <div
          class="home-header__shortcuts-item"
          v-if="features?.includes('insure.group')"
          @click="$router.push({ name: 'InsureGroup' })"
        >
          <van-image width="40" height="40" :src="require('@/assets/imgs/home/<USER>')" />
          <span>雇主责任险</span>
        </div>
        <div
          class="home-header__shortcuts-item"
          v-if="features?.includes('insure.general')"
          @click="$router.push({ name: 'InsureOther' })"
        >
          <van-image width="40" height="40" :src="require('@/assets/imgs/home/<USER>')" />
          <span>其他险</span>
        </div>
      </div>
      <van-swipe class="banner-swipe" :autoplay="5000" indicator-color="white">
        <van-swipe-item>
          <van-image height="125" width="100%" :src="require('@/assets/imgs/home/<USER>')" />
        </van-swipe-item>
      </van-swipe>
    </header>
    <van-notice-bar background="#fff" color="#ff7f4b" left-icon="volume" :text="announcement" />
    <main class="home-main">
      <header class="home-main__title">产品推荐</header>
      <ul class="home-main__products">
        <li
          class="home-main__products-item"
          v-for="(item, index) in pageData.recommendations"
          :key="index"
          @click="insureNow(item)"
        >
          <van-image width="100" height="87" fit="cover" :src="item.image" />
          <div class="product-content">
            <h3 class="product-content__title">{{ item.title }}</h3>
            <p class="product-content__desc">
              {{ item.content | stripTags }}
            </p>
            <div class="product-content__tags">
              <van-tag plain color="#ff7f4b" v-for="tag in item.tags?.split(',')" :key="tag">{{ tag }}</van-tag>
            </div>
            <div class="product-content__footer">
              <div class="footer-price">
                <label>{{ item.starting_price }}</label>
                <small>元/年起</small>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </main>
  </section>
</template>

<script>
import './style.less'
import { getHomePageShowData } from '@/apis/homePage'
import { latestAnnouncement } from '@/apis/announcement'
import { mapGetters } from 'vuex'

const headerBg = require('@/assets/imgs/home/<USER>')

export default {
  name: 'Home',
  data() {
    return {
      vConsoleCounter: 0,
      headerBg,
      product_type: [
        { label: '国内货运险', value: 1 },
        { label: '国际货运险', value: 2 },
        { label: '单车责任险', value: 3 },
        { label: '其他险种', value: 4 },
        { label: '雇主责任险', value: 5 }
      ],
      pageData: {
        user: {
          name: '',
          balance: ''
        },
        policy: {
          count: ''
        },
        messages: []
      },
      announcement: ''
    }
  },
  filters: {
    stripTags(value) {
      return value.replace(/(<([^>]+)>)/gi, '')
    }
  },
  computed: {
    ...mapGetters(['features'])
  },
  mounted() {
    this.getHomePageData()
  },
  methods: {
    handleClickUsername() {
      this.vConsoleCounter++

      if (this.vConsoleCounter >= 7) {
        document.querySelector('#__vconsole').style.display = 'block'
      }
    },
    getHomePageData() {
      latestAnnouncement().then((r) => {
        this.announcement = r.data?.data?.content || '暂无通知'
      })
      getHomePageShowData().then((r) => {
        this.pageData = r.data.data
      })
    },
    /* eslint-disable */
    insureNow(item) {
      switch (item.product_type) {
        case 1:
          this.$router.push({ name: 'InsureDomestic' })
          break
        case 2:
          this.$router.push({ name: 'InsureInternational' })
          break
        case 3:
          this.$router.push({ name: 'InsureLbt' })
          break
        case 4:
          this.$router.push({ name: 'InsureOther' })
          break
        case 5:
          this.$router.push({ name: 'InsureGroup' })
          break
      }
    }
    /* eslint-enable */
  }
}
</script>

<style lang="less" scoped>
.home-main {
  padding-bottom: 35px;
}

.home-main__products-item:not(:first-child) {
  margin-top: 15px;
}
.home-main__products-item {
  /deep/ .van-image {
    min-height: 96px;
  }

  .product-content__tags {
    gap: 0 !important;
  }

  .product-content {
    position: relative;
  }

  .van-tag:not(:first-child) {
    margin-left: 5px;
  }

  .product-content__desc {
    max-height: 26px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
  }

  .footer-price {
    position: absolute;
    bottom: 0;
    align-self: flex-end;
  }
}
</style>
