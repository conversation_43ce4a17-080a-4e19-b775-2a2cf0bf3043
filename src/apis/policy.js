/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2022-01-17 10:42:35
 * @LastEditors: yanb
 * @LastEditTime: 2023-11-27 16:50:17
 */
import * as _ from '@/utils/http.js'

// 国内货运险
export const preview = (data) => _.postFormDataOfArray('/policies/preview', data)

// 投保
export const insure = (data) => _.postFormDataOfArray('/policies', data)

// 国内货运险-标的
export const getSubjects = () => _.get('subjects')

// 国内货运-保险公司
export const getCompany = () => _.get('companies')

// 国内货运险
export const mainClause = (data) => _.get('products', data)

// 暂存单
export const staging = (data) => _.postFormDataOfArray('policies/draft', data)

// 保单详情
export const getPolicy = (id) => _.get(`/policies/${id}`)

// 修改保单
export const updatePolicy = (id, data) => _.postFormData(`policies/${id}/modify`, data)

// 其他险种支付记录
export const paymentHistory = (id) => _.get(`general-policies/${id}/transaction`)

// 其他险种
// 产品分类
export const productType = () => _.get('general-products/categories')
// 保险公司
export const company = () => _.get('companies')
// 获取列表
export const productList = (data) => _.get('general-products', data)
// 产品详情
export const productDetail = (id) => _.get(`general-products/${id}`)
// 其他险种投保
export const submitFrom = (data) => _.postFormData('policies', data)
// 上传凭证
export const uploadFile = (id, data) => _.postFormData(`general-policies/${id}/premium`, data)

// 雇主投保
// 获取雇主投保产品
export const getProductList = () => _.get('group-products')
// 提交雇主投保产品
export const gzsubmitFrom = (data) => _.postFormData('group-policies', data)
// 中意雇主产品
export const insureZhongyiGroup = (data) => _.post('group-policies', data)
// 获取暂存保单信息
export const insuredPolicyForm = (policyId) => _.get(`group-products/policy/${policyId}`)

// 获取纸质保单申请记录
export const getPolicyPapers = () => _.get(`policies/papers`)

// 申请纸质保单
export const applyPolicyPaper = (id, data) => _.post(`policies/${id}/papers`, data)

// 检查货物名称
export const getGoodsSuggestion = (data) => _.get('policies/goods-suggestion', data)

// 可理赔的保单列表
export const fetchClaimableList = (params) => _.get('policies/claimable', params)

// 可理赔保单
export const fetchClaimPolicy = (policyId) => _.get(`policies/claim/${policyId}`)

// 通过保单号搜索保单
export const searchByPolicyNo = (policyNo) => _.get('public/policy-searching', { policy_no: policyNo })
