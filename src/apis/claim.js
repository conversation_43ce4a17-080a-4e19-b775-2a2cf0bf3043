/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2023-10-18 11:34:48
 * @LastEditors: yanb
 * @LastEditTime: 2023-11-27 16:50:39
 */
import * as _ from '@/utils/http.js'

export const fetchSubmittedClaims = (params) => _.get('/claims', params)

export const fetchCaseDetail = (id) => _.get(`/claims/${id}`)

export const cancelClaim = (id) => _.patch(`/claims/${id}/cancellation`)

export const createClaim = (data) => _.post('/claims', data)

export const uploadAttachment = (id, data) => _.postFormData(`claims/${id}/attachments`, data)

export const hurryUp = (id) => _.patch(`claims/${id}/hurry-up`)

export const publicCreateClaim = (data) => _.post('public/claims/create', data)

export const fetchPublicClaims = (params) => _.get('public/claims', params)

export const fetchPublicCaseDetail = (id) => _.get(`public/claims/${id}`)

export const publicUploadAttachment = (id, data) => _.postFormData(`public/claims/${id}/attachments`, data)

export const publicHurryUp = (id) => _.patch(`public/claims/${id}/hurry-up`)

export const publicCancelClaim = (id) => _.patch(`public/claims/${id}/cancellation`)
