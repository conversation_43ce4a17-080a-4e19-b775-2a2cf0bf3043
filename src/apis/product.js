import { get } from '@/utils/http'

// 获取产品.
export const getProducts = (data, query) => get('products', Object.assign({}, { filter: data }, query))

// 获取产品货物类别.
export const getProductGoodsTypes = (id) => get(`products/${id}/goods-types`)

// 获取产品包装方式.
export const getProductPackingMethods = (id) => get(`products/${id}/packing-methods`)

// 获取产品装载方式.
export const getProductLoadingMethods = (id) => get(`products/${id}/loading-methods`)

// 获取产品运输方式.
export const getProductTransportMethods = (id) => get(`products/${id}/transport-methods`)

// 获取雇主产品详情
export const getGroupProduct = (id) => get(`group-products/${id}`)
