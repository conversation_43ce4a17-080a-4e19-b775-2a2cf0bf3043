import { post, patch, get, put } from '@/utils/http.js'

// 登录接口
export const login = (data) => post('auth/login', data)
// 注册接口
export const register = (data) => post('auth/registration', data)
// 注册-验证码
export const sendYzm = (data) => post('verification-code', data)
// 登出
export const logout = () => post('auth/logout')

// 修改密码
export const editPassword = (data) => patch('users/me/password', data)
// 充值记录
export const payments = (data) => get('payments', data)

//在线充值
export const onlineRecharge = (data) => post(`payments/online-recharge`, data)

// 获取当前登录用户.
export const getUser = () => get(`users/me`)
// 更新用户信息
export const updateUser = (data) => put(`users/me`, data)
// 用户消息
export const messageList = () => get('users/me/messages')
// 标记为已读.
export const markMessageAsRead = (id) => patch(`users/me/messages/${id}`)
// 获取验证码
export const captchaSrc = () => get('captcha')

// 充值
export const charge = (id, data) => patch(`users/${id}/charge`, data)

// 扣费
export const pay = (id, data) => patch(`users/${id}/pay`, data)

// 获取用户列表.
export const getUsers = (data) => get(`users`, data)

// 更新用户密码
export const updatePassword = (id = 'me', data) => patch(`users/${id}/password`, data)

// 更新用户信息
export const updateMyUser = (id, data) => put(`users/${id}`, data)

// 忘记密码验证吗
export const sendForgotPasswordCaptcha = (data) => get(`auth/forgot-password-captcha`, data)

// 重置密码
export const resetPassword = (data) => post(`auth/reset-password`, data)
