import * as _ from '@/utils/http'
import { tokenKey } from '../config'

// 表单作废
export const zfTable = (id, data) => _.post(`policies/${id}/drop`, data)

export const mainList = (data) => _.get('policies', data)
// 雇主险列表
export const gzList = (data) => _.get('group-policies', data)
// 雇主险列表
export const gzDetail = (id) => _.get(`group-policies/${id}`)
// 保单详情
export const manageDetail = (id) => _.get(`policies/${id}`)
// 修改表单
export const editFromInfo = (id, data) => _.postFormData(`policies/${id}/modify`, data)

// 作废暂存单
export const destroyDraft = (id) => _.patch(`policies/${id}/drop`)

// 退保
export const surrender = (id, data) => _.patch(`policies/${id}/surrender`, data)

/**
 * @deprecated Due to bad method name.
 */
export const returnReason = (id, data) => _.patch(`policies/${id}/surrender`, data)

// 下载保单
export const downloadFrom = (id) => {
  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  const token = window.localStorage.getItem(tokenKey)
  return `${baseUrl}policies/${id}/download?token=${token}`
}

// 雇主险-未提批人员列表
export const peopleList = (id, data) => _.get(`group-policies/employees/${id}/endorse`, data)
// 雇主先-在保人员列表
export const newPeopleList = (id, data) => _.get(`group-policies/employees/${id}`, data)
// 添加人员
export const addPeople = (data) => _.post(`group-policies/employees/modify`, data)
// 获取保单已投产品信息
export const getDetail = (id) => _.get(`group-products/policy/${id}`)
// 撤销在保人员
export const returnPeople = (id) => _.del(`/group-policies/employees/modify/${id}`)
// 保费计算
export const getPayMoney = (id) => _.get(`group-policies/employees/${id}/premium`)
// 提交
export const submitMoney = (id, data) => _.postFormData(`group-policies/employees/${id}/submit`, data)
// 在保人员批件
export const peoplePj = (data) => _.post('group-policies/employees/modify', data)
// 替换
export const replacePeople = (data) => _.post('group-policies/employees/modify', data)
// 雇主-支付记录
export const payhistory = (id) => _.get(`group-policies/transactions/${id}`)
// 暂存单作废
export const invalidPolicy = (id) => _.post(`group-policies/${id}/invalid`)
// 雇主人员批单
export const groupEndorses = (policyGroupId) => _.get(`group-policies/endorses/${policyGroupId}`)

// 下载雇主批单
export const buildEndorseDownloadHref = (id) => {
  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  const token = window.localStorage.getItem(tokenKey)
  return `${baseUrl}group-policies/endorses/detail/${id}/download?token=${token}`
}

// 未支付的订单.
export const unpaidPayments = (id) => _.get(`policies/${id}/payments/unpaid`)

// 支付订单
export const getUnpaidPayment = (id, paymentId, data) =>
  _.get(`policies/${id}/payments/${paymentId}/pay`, data)
