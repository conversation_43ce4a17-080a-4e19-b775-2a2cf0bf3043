<template>
  <div id="app" :style="{ '--primary-color': primaryColor }">
    <router-view></router-view>
  </div>
</template>

<style lang="less"></style>

<script>
import { findByDomain } from '@/apis/platform'

export default {
  computed: {
    primaryColor() {
      return this.$store.state.primaryColor
    }
  },
  async mounted() {
    try {
      const response = await findByDomain(window.location.hostname)
      document.title = response.data.data.title

      if (response.data.data.primary_color) {
        this.$store.dispatch('setPrimaryColor', response.data.data.primary_color)
      }
      if (response.data.data.features) {
        this.$store.dispatch('setFeatures', response.data.data.features)
      }
      if (response.data.data.online_payment_is_enabled) {
        this.$store.dispatch('setOnlinePaymentIsEnabled', response.data.data.online_payment_is_enabled)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    }
  }
}
</script>

<style>
#__vconsole {
  display: none;
}
</style>
