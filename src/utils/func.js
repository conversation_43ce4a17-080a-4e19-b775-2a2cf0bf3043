import { areaList } from '@vant/area-data'
import imageCompression from 'browser-image-compression'

export const array2Tree = (
  jsonArr = [],
  selfIdField = 'id',
  parentIdField = 'parentId',
  parentld = '',
  selfIdFieldName = '',
  parentName = ''
) => {
  const result = []
  for (const val of jsonArr) {
    if (val[parentIdField] === parentld) {
      const obj = {}
      for (const key in val) {
        obj[key] = val[key]
      }
      const children = array2Tree(
        jsonArr,
        selfIdField,
        parentIdField,
        obj[selfIdField],
        selfIdFieldName,
        parentName ? obj[parentName] : undefined
      )
      obj.parent_name = selfIdFieldName ? obj[selfIdFieldName] : undefined
      obj.children = children.length > 0 ? children : undefined
      result.push(obj)
    }
  }
  return result
}

export const findAreaCode = (area) => {
  if (!area) {
    return null
  }

  const [province, city] = area.split('-')

  if (city !== undefined) {
    return Object.keys(areaList.city_list).find((code) => {
      return areaList.city_list[code] === city
    })
  }

  return Object.keys(areaList.province_list).find((code) => {
    return areaList.province_list[code] === province
  })
}

export const imageCompressor = async (imageFile) => {
  const options = {
    maxSizeMB: 2,
    useWebWorker: false
  }

  try {
    return await imageCompression(imageFile, options)
  } catch (e) {
    return imageFile
  }
}

export const arraysEqual = (a, b) => {
  a = a.sort()
  b = b.sort()
  if (a === b) return true
  if (a == null || b == null || a.length !== b.length) return false

  for (var i = 0; i < a.length; ++i) {
    if (a[i] !== b[i]) return false
  }

  return true
}

/** 金额转大写 */
export const digitUppercase = (money) => {
  const fraction = ['角', '分']
  const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  const unit = [
    ['元', '万', '亿'],
    ['', '拾', '佰', '仟']
  ]
  const head = money < 0 ? '欠' : ''
  money = Math.abs(money)
  let s = ''
  for (let i = 0; i < fraction.length; i++) {
    s += (digit[Math.floor(money * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '')
  }
  s = s || '整'
  money = Math.floor(money)
  for (let i = 0; i < unit[0].length && money > 0; i++) {
    let p = ''
    for (let j = 0; j < unit[1].length && money > 0; j++) {
      p = digit[money % 10] + unit[1][j] + p
      money = Math.floor(money / 10)
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s
  }
  return (
    head +
    s
      .replace(/(零.)*零元/, '元')
      .replace(/(零.)+/g, '零')
      .replace(/^整$/, '零元整')
  )
}
