import axios from 'axios'
import { token<PERSON><PERSON> } from '@/config'
import router from '@/router'
import { Toast } from 'vant'
import qs from 'qs'

const config = {
  baseURL: process.env.VUE_APP_BASE_API,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  },
  timeout: 60 * 1000,
  withCredentials: false
}
const instance = axios.create(config)

// 添加请求拦截器
instance.interceptors.request.use(
  (config) => {
    if (window.localStorage.getItem(tokenKey)) {
      config.headers.Authorization = 'Bearer ' + window.localStorage.getItem(tokenKey)
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 添加响应拦截器
instance.interceptors.response.use(
  (res) => res,
  (error) => {
    if (error.response && error.response.status === 401 && router.history.current.name !== 'login') {
      localStorage.removeItem(tokenKey)
      router.push({ name: 'login' })
    } else if (error.response && error.response.status === 422) {
      let messages = ''
      const errors = error.response.data.errors || error.response.data.message
      Object.keys(errors).forEach((e) => {
        if (Array.isArray(errors)) {
          errors[e].forEach((e) => {
            messages += e.replace(' ', '') + '\n'
          })
        } else {
          messages += errors[e].replace(' ', '') + '\n'
        }
      })

      Toast.fail(messages)
    } else {
      const message = error.response.data.message || 'System error'
      if (message.indexOf('Token not provided') !== -1 || message.indexOf('Token has expired') !== -1) {
        Toast.fail('登录失效')
      } else {
        Toast.fail(message)
      }
    }

    return Promise.reject(error)
  }
)

export const get = (url, params = {}) => instance.get(url + '?' + qs.stringify(params))

export const post = async (url, params = {}) => instance.post(url, params)

export const postForm = (url, params) =>
  instance.post(url, params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    transformRequest: [
      (data) => {
        return qs.stringify(data)
      }
    ]
  })

export const del = async (url, params = {}) =>
  instance.delete(url, {
    params
  })

export const put = async (url, params = {}) => instance.put(url, params, config)

export const patch = async (url, params = {}) => instance.patch(url, params)

/**
 * postFormData方法，对应post请求，用来提交文件+数据
 *
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
export const postFormData = (url, params = {}) => {
  const formData = new FormData()
  Object.keys(params).forEach((k) => formData.append(k, params[k]))

  config.headers['Content-Type'] = 'multipart/form-data'

  return instance.post(url, formData, config)
}

/**
 * postFormData方法，对应post请求，用来提交文件+数据
 *
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
export const postFormDataOfArray = (url, params = {}) => {
  const formData = new FormData()

  Object.keys(params).forEach((k) => {
    if (Object.prototype.toString.call(params[k]) === '[object Array]') {
      params[k].forEach((item, index) => {
        formData.append(`${k}[${index}]`, item)
      })
    } else {
      formData.append(k, params[k])
    }
  })

  config.headers['Content-Type'] = 'multipart/form-data'

  return instance.post(url, formData, config)
}
