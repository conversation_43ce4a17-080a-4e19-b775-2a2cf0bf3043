import areadata from './areadata.json'

export default class AddressParser {
  static provinceSeparate = [
    '特别行政区',
    '古自治区',
    '维吾尔自治区',
    '壮族自治区',
    '回族自治区',
    '自治区',
    '自治区直辖行政单位',
    '省省直辖',
    '省',
    '市',
    '地区'
  ]

  static specialCities = ['北京市', '上海市', '天津市', '重庆市']

  static citySeparate = ['布依族苗族自治州', '苗族侗族自治州', '自治州', '市']

  static countySeparate = ['县', '区']

  static parseProvince(address) {
    let province = ''
    let provinceIdx = -1
    for (let i = 0; i < this.provinceSeparate.length; i++) {
      const sep = this.provinceSeparate[i]
      provinceIdx = address.indexOf(sep)
      if (provinceIdx !== -1) {
        province = address.substring(0, provinceIdx) + sep
        if (sep === '市' && this.specialCities.indexOf(province) === -1) {
          province = ''
          provinceIdx = -1
        } else {
          provinceIdx += sep.length - 1
        }

        break
      }
    }

    return { provinceIdx, province }
  }

  static parseCity(province, provinceIdx, address) {
    let city = ''
    const separate = this.citySeparate.concat(this.countySeparate)

    for (let i = 0; i < separate.length; i++) {
      const sep = separate[i]
      const sepIdx = address.indexOf(sep)
      if (sepIdx !== -1 && sepIdx > provinceIdx) {
        city = address.substring(provinceIdx + 1, sepIdx) + sep
        break
      }
    }

    if (this.specialCities.indexOf(province) >= 0) {
      return city
    }

    if (this.isCountyLevelCity(city)) {
      areadata.forEach((province) => {
        for (let i = 0; i < province.city.length; i++) {
          const c = province.city[i]
          if (c.county.findIndex((d) => d.value === city) >= 0) {
            city = c.value
            break
          }
        }
      })
    }

    return city
  }

  static isCountyLevelCity(city) {
    return (
      areadata.find((item) => item?.city?.findIndex((c) => c.county?.findIndex((d) => d.value === city) >= 0) >= 0) !==
      undefined
    )
  }

  static findProvince(city) {
    return areadata.find((item) => item?.city?.findIndex((c) => c.value === city) >= 0)?.value
  }

  static findByCounty(address) {
    let county = ''
    for (let i = 0; i <= this.countySeparate.length; i++) {
      const sep = this.countySeparate[i]
      const idx = address.indexOf(sep)
      if (idx !== -1) {
        county = address.substring(0, idx) + sep
        break
      }
    }

    let province = {}
    let city = {}
    for (let i = 0; i < areadata.length; i++) {
      province = areadata[i]
      city = province.city?.find((c) => c?.county?.findIndex((d) => d.value === county) >= 0)
      if (city) break
    }

    if (!city) province = {}

    const provinceName = province?.value
    const cityName = city?.value

    return { provinceName, cityName, county }
  }

  static parse(address) {
    if (!address) {
      return { province: '', city: '' }
    }

    address = address.trim()
    let { provinceIdx, province } = this.parseProvince(address)
    let city = this.parseCity(province, provinceIdx, address)
    if (city && !province) {
      province = this.findProvince(city)
    }

    if (!city && !province) {
      const found = this.findByCounty(address)
      province = found.provinceName
      city = found.cityName
    }

    if (!province) {
      city = ''
    }

    return {
      province,
      city
    }
  }
}
