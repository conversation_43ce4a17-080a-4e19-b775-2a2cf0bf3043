import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

import 'lib-flexible/flexible'
import Vant from 'vant'
import 'vant/lib/index.css'
import 'normalize.css'
import './assets/style/global.scss'

import Download from './plugins/download'

import VConsole from 'vconsole'
import Devtools from 'vue-vconsole-devtools'
Devtools.initPlugin(new VConsole())

Vue.use(Vant)
Vue.use(Download)

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')
