<template>
  <div>
    <van-field
      readonly
      clickable
      :value="keys"
      :placeholder="tips"
      @click="showPicker = true"
      :right-icon="arrow && 'arrow'"
      :label="label"
      :rules="[{ required: isRequire, message: '此项为必填项' }]"
    >
      <template #label>
        <span class="required" v-if="isRequire">{{ label }}</span>
      </template>
    </van-field>
    <!-- 弹出层 -->
    <van-popup v-model="showPicker" position="bottom">
      <van-picker show-toolbar :columns="columns" @confirm="clickPopup" @cancel="showPicker = false" />
    </van-popup>
  </div>
</template>

<script>
export default {
  props: ['label', 'tips', 'columns', 'isRequire', 'arrow', 'rid'],
  data() {
    return {
      showPicker: false,
      keys: void 0
    }
  },
  mounted() {
    setTimeout(() => {
      if (this.rid) {
        this.columns.forEach((item, index) => {
          if (item.id === this.rid) {
            this.keys = this.columns[index].name
          }
        })
      }
    }, 1000)
  },
  methods: {
    clickPopup(value) {
      this.$emit('input', value.id)
      this.keys = value.name
      this.showPicker = false
    }
  }
}
</script>

<style lang="less" scoped>
// 必填项
.required::after {
  content: '*';
  color: red;
}
</style>
