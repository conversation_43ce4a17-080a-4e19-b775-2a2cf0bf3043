import Vue from 'vue'
import VueRouter from 'vue-router'
import Index from '@/views/Index.vue'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

Vue.use(VueRouter)

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/login')
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/views/auth/register')
  },
  {
    path: '/forgot-password',
    name: 'forgot-password',
    component: () => import('@/views/auth/forgotPassword')
  },
  {
    path: '/claim-home',
    name: 'PublicClaimHome',
    component: () => import('@/views/claim/Public/Home')
  },
  {
    path: '/submission-claim',
    name: 'SubmissionClaim',
    component: () => import('@/views/claim/Public/Submission')
  },
  {
    path: '/p-claims',
    name: 'PublicClaimProgress',
    component: () => import('@/views/claim/Public/Progress')
  },
  {
    path: '/p-claims/:id',
    name: 'PublicClaimProgressDetail',
    component: () => import('@/views/claim/Public/ProgressDetail.vue')
  },
  {
    path: '/',
    name: 'index',
    component: Index,
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('@/views/home/<USER>')
      },
      {
        path: '/insure',
        name: 'insure',
        component: () => import('@/views/insure/Insure.vue')
      },
      {
        path: '/policies',
        name: 'Policies',
        component: () => import('@/views/policies/Policies.vue')
      },
      {
        path: '/my',
        name: 'My',
        component: () => import('@/views/my/My.vue')
      },
      {
        path: '/claimable',
        name: 'Claimable',
        component: () => import('@/views/claim/Claimable.vue')
      },
      {
        path: '/claim-submission/:id',
        name: 'ClaimSubmission',
        component: () => import('@/views/claim/Submission.vue')
      },
      {
        path: '/progress',
        name: 'ClaimProgress',
        component: () => import('@/views/claim/Progress.vue')
      },
      {
        path: '/progress/:id',
        name: 'ClaimProgressDetail',
        component: () => import('@/views/claim/ProgressDetail.vue')
      }
    ]
  },
  // 国内货运
  {
    path: '/insure/domestic',
    name: 'InsureDomestic',
    component: () => import('@/views/insure/domestic')
  },
  // 单车
  {
    path: '/insure/lbt',
    name: 'InsureLbt',
    component: () => import('@/views/insure/lbt')
  },
  // 国际货运
  {
    path: '/insure/international',
    name: 'InsureInternational',
    component: () => import('@/views/insure/intl')
  },
  // 雇主
  {
    path: '/insure/group',
    name: 'InsureGroup',
    component: () => import('@/views/insure/group/Index.vue')
  },
  {
    path: '/insure/group/form/basic',
    name: 'InsureGroupBasic',
    component: () => import('@/views/insure/group/FormBasic.vue')
  },
  {
    path: '/insure/group/form/zhongyi',
    name: 'InsureGroupZhongyi',
    component: () => import('@/views/insure/group/FormZhongyi.vue')
  },

  // 其他
  {
    path: '/insure/other',
    name: 'InsureOther',
    component: () => import('@/views/insure/other/Index.vue')
  },
  {
    path: '/insure/other/details/:id',
    name: 'InsureOtherDetail',
    component: () => import('@/views/insure/other/Detail.vue')
  },
  {
    path: '/insure/other/form/:id',
    name: 'InsureOtherForm',
    component: () => import('@/views/insure/other/Form.vue')
  },
  {
    path: '/insure/other/pay/:id',
    name: 'InsureOtherPay',
    component: () => import('@/views/insure/other/Pay.vue')
  },
  // 我的-我的资料
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/my/pages/Profile.vue')
  },
  // 我的-充值记录
  {
    path: '/charge-records',
    name: 'ChargeRecords',
    component: () => import('@/views/my/pages/ChargeRecords.vue')
  },
  // 我的-修改密码
  {
    path: '/password',
    name: 'UpdatePassword',
    component: () => import('@/views/my/pages/UpdatePassword.vue')
  },
  // 我的-消息
  {
    path: '/messages',
    name: 'Messages',
    component: () => import('@/views/my/pages/Message.vue')
  },
  // 我的-客户列表
  {
    path: '/my/users',
    name: 'MyUsers',
    component: () => import('@/views/my/pages/Users.vue')
  },

  // 保单管理-国内货运险
  {
    path: '/policies/domestic/:id',
    name: 'DomesticDetail',
    component: () => import('@/views/policies/order/DomesticDetail.vue')
  },
  // 保单管理-国际货运险-已出单
  {
    path: '/policies/intl/:id',
    name: 'IntlDetail',
    component: () => import('@/views/policies/order/IntlDetail.vue')
  },
  // 纸质保单
  {
    path: '/policies/:id/paper',
    name: 'PoliciesPaper',
    component: () => import('@/views/policies/order/Paper.vue')
  },
  // 保单管理-单车-已出单
  {
    path: '/policies/lbt/:id',
    name: 'LbtDetail',
    component: () => import('@/views/policies/order/LbtDetail.vue')
  },
  // 保单管理-其他险种
  {
    path: '/policies/other/:id',
    name: 'OtherDetail',
    component: () => import('@/views/policies/order/OtherDetail.vue')
  },
  {
    path: '/policies/other/:id/payment-history',
    name: 'OtherPaymentHistory',
    component: () => import('@/views/policies/order/OtherPaymentHistory.vue')
  },
  // 保单管理-雇主
  {
    path: '/policies/group/:id',
    name: 'GroupDetail',
    component: () => import('@/views/policies/order/GroupDetail.vue')
  },
  // 保单管理-雇主险-人员列表
  {
    path: '/policies/group/people-lists/:id',
    name: 'GroupPeopleList',
    component: () => import('@/views/policies/order/GroupPeopleList.vue')
  },
  {
    path: '/policies/group/endorses/:id',
    name: 'GroupEndorses',
    component: () => import('@/views/policies/order/GroupEndorse.vue')
  },
  // 保单管理-雇主险-人员列表-添加人员
  {
    path: '/policies/group/add-people/:id',
    name: 'GroupAddPeople',
    component: () => import('@/views/policies/order/GroupAddPeople.vue')
  },

  // 保单管理-雇主险-人员列表-替换
  {
    path: '/policies/group/replace-people/:employee_id/:policy_group_id',
    name: 'GroupReplacePeople',
    component: () => import('@/views/policies/order/GroupReplacePeople.vue')
  },

  // 保单管理-雇主险-支付管理
  {
    path: '/policies/group/payments/:id',
    name: 'GroupPaymentManagement',
    component: () => import('@/views/policies/order/GroupPayManagement.vue')
  },

  // 保单管理-线下录入保单
  {
    path: '/policies/offline/:id',
    name: 'OfflineDetail',
    component: () => import('@/views/policies/order/OfflineDetail.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.NODE_ENV === 'production' ? '/mobile/' : '/',
  routes
})

router.beforeEach((to, from, next) => {
  NProgress.start()
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
