import { Dialog, Toast } from "vant"

export default {
  isWeChat() {
    return window.navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1
  },
  fallback(text) {
    Dialog.alert({
      title: '下载失败',
      message: `您的浏览器不支持自动复制功能，请手动复制下面链接到浏览器中打开完成下载` +
        `<input style="width:100%;height:1rem;border:1px solid #eee;box-sizing:border-box;padding-left:0.4rem;padding-right:0.4rem;border-radius:.1rem;" value="${text}"></input>`,
    })
  },
  copy(text) {
    if (!window.navigator.clipboard) {
      this.fallback(text)
    } else {
      window.navigator.clipboard.writeText(text).then(() => {
        Toast.success('复制成功')
      }).catch(() => {
        this.fallback(text)
      })
    }
  },
  install(Vue, options) {
    // Download method
    Vue.prototype.$download = (href) => {
      if (!this.isWeChat()) {
        window.location.href = href
      } else {
        Dialog.confirm({
            title: '下载提醒',
            message: `受微信限制微信应用内暂时无法下载保单文件，点击复制按钮系统将会自动为您复制下载链接，您可打开浏览器粘贴完成保单下载`,
            theme: 'round-button',
            confirmButtonText: '复制',
          })
          .then(() => {
            this.copy(href)
          })
          .catch(() => {
            //
          })
      }
    }
  }
}
