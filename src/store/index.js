import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    features: [],
    primaryColor: '#ff7f4c',
    onlinePaymentIsEnabled: 0
  },
  getters: {
    features: (state) => state.features,
    primaryColor: (state) => state.primaryColor,
    onlinePaymentIsEnabled: (state) => state.onlinePaymentIsEnabled
  },
  mutations: {
    UPDATE_PRIMARY_COLOR(state, color) {
      state.primaryColor = color
    },
    UPDATE_FEATURES(state, features) {
      state.features = features
    },
    UPDATE_ONLINE_PAYMENT_IS_ENABLED(state, isEnabled) {
      state.onlinePaymentIsEnabled = isEnabled
    }
  },
  actions: {
    setFeatures({ commit }, features) {
      commit('UPDATE_FEATURES', features)
    },
    setPrimaryColor({ commit }, color) {
      commit('UPDATE_PRIMARY_COLOR', color)
    },
    setOnlinePaymentIsEnabled({ commit }, isEnabled) {
      commit('UPDATE_ONLINE_PAYMENT_IS_ENABLED', isEnabled)
    }
  },
  modules: {}
})
