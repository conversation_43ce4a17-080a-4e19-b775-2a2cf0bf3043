{"name": "baoya", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve --open", "serve": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve --mode local", "build:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build  --mode production", "build:staging": "cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --mode staging", "lint": "vue-cli-service lint"}, "dependencies": {"@vant/area-data": "^1.1.1", "axios": "^0.21.1", "browser-image-compression": "^1.0.15", "core-js": "^3.6.5", "dayjs": "^1.10.5", "lib-flexible": "^0.3.2", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "qs": "^6.10.1", "regenerator-runtime": "^0.13.9", "vant": "^2.12.18", "vconsole": "^3.9.1", "vue": "^2.6.11", "vue-router": "^3.2.0", "vue-vconsole-devtools": "^0.0.7", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.10", "@vue/cli-plugin-eslint": "~4.5.10", "@vue/cli-plugin-router": "~4.5.10", "@vue/cli-plugin-vuex": "~4.5.10", "@vue/cli-service": "~4.5.10", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^6.8.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.4.1", "less": "^3.0.0", "less-loader": "^5.0.0", "node-sass": "^7.0.3", "postcss-pxtorem": "^5.0.0", "prettier": "^2.2.1", "qs": "^6.9.4", "sass-loader": "^10.1.0", "vue-template-babel-compiler": "^1.0.3", "vue-template-compiler": "^2.6.12"}}