
const webpackConfig = {
  lintOnSave: false,
  assetsDir: 'static',
  productionSourceMap: false,
  publicPath: process.env.NODE_ENV === 'production' ? 'https://yrt-static.oss-cn-hangzhou.aliyuncs.com/baoya-mobile' : '/',
  // 开发环境及代理配置
  devServer: {
    port: '9200',
    disableHostCheck: true,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_BASE_API,
        changeOrigin: true,
        pathRewrite: {
          '/api': ''
        }
      }
    }
  },
  chainWebpack: (config) => {
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap((options) => {
        options.compiler = require('vue-template-babel-compiler')
        return options
      })
  }
}

module.exports = webpackConfig
