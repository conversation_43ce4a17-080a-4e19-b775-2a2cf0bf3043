name: Build and Deploy

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Aliyun CLI
        run: |
          sed -i 's/archive.ubuntu.com/mirrors.cloud.aliyuncs.com/g' /etc/apt/sources.list.d/ubuntu.sources
          apt-get update -y && apt-get install --no-install-recommends -y curl unzip
          curl https://gosspublic.alicdn.com/ossutil/install.sh | bash

      # Due to the network issue in China, we use nodesource setup script to install nodejs.
      - name: Set up Nodejs
        run: |
          curl -fsSL https://deb.nodesource.com/setup_18.x -o nodesource_setup.sh
          bash nodesource_setup.sh
          apt-get install -y nodejs

      - name: Get npm cache directory
        id: npm-cache-dir
        shell: bash
        run: echo "dir=$(npm config get cache)" >> ${GITHUB_OUTPUT}

      - name: Cache npm dependencies
        uses: actions/cache@v4
        id: npm-cache
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: SASS_BINARY_SITE=https://npmmirror.com/mirrors/node-sass/ npm install --registry https://registry.npmmirror.com

      - name: Build the project for production
        if: github.ref == 'refs/heads/main'
        run: npm run build:prod

      - name: Build the project for staging
        if: github.ref == 'refs/heads/develop'
        run: npm run build:staging

      - name: Upload Assets to OSS
        run: |
          ossutil -e oss-cn-hangzhou.aliyuncs.com \
          -i ${{ secrets.OSS_ACCESS_KEY_ID}} \
          -k ${{ secrets.OSS_ACCESS_SECRET }} \
          --region cn-hangzhou \
          cp -r -f --exclude index.html dist oss://yrt-static/baoya-mobile

      - name: Export environment variables for production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "APP_PATH=/home/<USER>/app/baoya/user/mobile" >> $GITHUB_ENV
          echo "SSH_HOST=${{ vars.SSH_PROD_HOST }}" >> $GITHUB_ENV
          echo "SSH_USER=${{ vars.SSH_PROD_USER }}" >> $GITHUB_ENV

      - name: Export environment variables for staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "APP_PATH=/var/www/new_51baoya/user/mobile" >> $GITHUB_ENV
          echo "SSH_HOST=${{ vars.SSH_STAGING_HOST }}" >> $GITHUB_ENV
          echo "SSH_USER=${{ vars.SSH_STAGING_USER }}" >> $GITHUB_ENV

      - name: Upload index.html
        env:
          APP_PATH: ${{ env.APP_PATH }}
          SSH_HOST: ${{ env.SSH_HOST }}
          SSH_USER: ${{ env.SSH_USER }}
        run: |
          if [ "$GITHUB_REF" == "refs/heads/main" ]; then
            SSH_PRIVATE_KEY="${{ secrets.SSH_PROD_PRIVATE_KEY }}"
          else
            SSH_PRIVATE_KEY="${{ secrets.SSH_STAGING_PRIVATE_KEY }}"
          fi

          if [ -z "$SSH_PRIVATE_KEY" ]; then
            echo "SSH_PRIVATE_KEY is not set"
            exit 1
          fi

          mkdir -p "$HOME/.ssh"
          echo "$SSH_PRIVATE_KEY" > "$HOME/.ssh/id_rsa"
          chmod 600 "$HOME/.ssh/id_rsa"
          scp -i "$HOME/.ssh/id_rsa" -o StrictHostKeyChecking=no dist/index.html "$SSH_USER@$SSH_HOST:$APP_PATH/index.html"
